import { Request, Response } from "express";
import backupService from "../services/backup.service";
import { asyncHandler } from "../middleware/error-handler.middleware";
import { logError } from "../utils/logger";
import { z } from "zod";

/**
 * <PERSON><PERSON>a xác thực cho các yêu cầu liên quan đến sao lưu
 */
const scheduleBackupSchema = z.object({
  cronExpression: z.string().optional(),
});

const deleteBackupSchema = z.object({
  backupName: z.string().min(1, "Tên bản sao lưu không được để trống"),
});

/**
 * Controller x<PERSON> lý các tác vụ liên quan đến sao lưu
 */
class BackupController {
  /**
   * Thực hiện sao lưu thủ công
   * @route POST /api/backups/manual
   */
  public manualBackup = asyncHandler(async (_req: Request, res: Response) => {
    try {
      const result = await backupService.performBackup();

      if (result.success) {
        return res.status(200).json({
          success: true,
          data: {
            backupPath: result.backupPath,
            timestamp: result.timestamp,
            totalFiles: result.totalFiles,
          },
          message: "Đã thực hiện sao lưu thủ công thành công",
        });
      } else {
        return res.status(500).json({
          success: false,
          error: result.error,
        });
      }
    } catch (error) {
      logError("Lỗi khi thực hiện sao lưu thủ công", error);
      throw error;
    }
  });

  /**
   * Lên lịch sao lưu tự động
   * @route POST /api/backups/schedule
   */
  public scheduleBackup = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Xác thực dữ liệu đầu vào
      const validatedData = scheduleBackupSchema.parse(req.body);

      // Khởi tạo lịch trình sao lưu
      backupService.initScheduledBackup(validatedData.cronExpression);

      return res.status(200).json({
        success: true,
        message: "Đã lên lịch sao lưu tự động thành công",
        cronExpression:
          validatedData.cronExpression || backupService.getCronExpression(),
      });
    } catch (error) {
      // Xử lý lỗi xác thực Zod
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: error.errors[0].message,
        });
      }

      // Xử lý lỗi biểu thức cron không hợp lệ
      if (
        error instanceof Error &&
        error.message.includes("cron không hợp lệ")
      ) {
        return res.status(400).json({
          success: false,
          error: error.message,
        });
      }

      logError("Lỗi khi lên lịch sao lưu tự động", error);
      throw error;
    }
  });

  /**
   * Dừng lịch trình sao lưu tự động
   * @route POST /api/backups/stop-schedule
   */
  public stopScheduledBackup = asyncHandler(
    async (_req: Request, res: Response) => {
      backupService.stopScheduledBackup();

      return res.status(200).json({
        success: true,
        message: "Đã dừng lịch trình sao lưu tự động",
      });
    }
  );

  /**
   * Lấy danh sách các bản sao lưu
   * @route GET /api/backups
   */
  public getBackupList = asyncHandler(async (_req: Request, res: Response) => {
    try {
      const backups = await backupService.getBackupList();

      return res.status(200).json({
        success: true,
        count: backups.length,
        data: backups,
      });
    } catch (error) {
      logError("Lỗi khi lấy danh sách bản sao lưu", error);
      throw error;
    }
  });

  /**
   * Xóa một bản sao lưu
   * @route DELETE /api/backups
   */
  public deleteBackup = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Xác thực dữ liệu đầu vào
      const validatedData = deleteBackupSchema.parse(req.body);

      // Xóa bản sao lưu
      const success = await backupService.deleteBackup(
        validatedData.backupName
      );

      return res.status(200).json({
        success,
        message: `Đã xóa bản sao lưu: ${validatedData.backupName}`,
      });
    } catch (error) {
      // Xử lý lỗi xác thực Zod
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: error.errors[0].message,
        });
      }

      // Xử lý lỗi bản sao lưu không tồn tại
      if (error instanceof Error && error.message.includes("không tồn tại")) {
        return res.status(404).json({
          success: false,
          error: error.message,
        });
      }

      logError("Lỗi khi xóa bản sao lưu", error);
      throw error;
    }
  });
}

export default new BackupController();
