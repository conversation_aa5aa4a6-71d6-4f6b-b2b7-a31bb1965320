import { z } from "zod";
import path from "path";
import dotenv from "dotenv";

// Tải biến môi trường
dotenv.config();

// Schema xác thực cho cấu hình
const configSchema = z.object({
  port: z
    .string()
    .default("3000")
    .transform((val) => parseInt(val, 10)),
  nodeEnv: z.string().default("development"),

  // Giới hạn tệp
  maxFileSize: z
    .string()
    .default("5242880")
    .transform((val) => parseInt(val, 10)), // 5MB

  // Đường dẫn
  uploadDir: z.string().default("src/public/uploads"),
  backupDir: z.string().default("backup-images"),

  // Cài đặt sao lưu
  backupInterval: z.string().default("0 0 * * *"), // Mỗi ngày lúc 0:00

  // Thiết lập hình ảnh
  imageQuality: z
    .string()
    .default("80")
    .transform((val) => parseInt(val, 10)),
  imageWidth: z
    .string()
    .default("800")
    .transform((val) => parseInt(val, 10)),
  imageHeight: z
    .string()
    .default("600")
    .transform((val) => parseInt(val, 10)),
});

// Tạo cấu hình từ biến môi trường
const config = {
  port: process.env.PORT,
  nodeEnv: process.env.NODE_ENV,
  maxFileSize: process.env.MAX_FILE_SIZE,
  uploadDir: process.env.UPLOAD_DIR,
  backupDir: process.env.BACKUP_DIR,
  backupInterval: process.env.BACKUP_INTERVAL,
  imageQuality: process.env.IMAGE_QUALITY,
  imageWidth: process.env.IMAGE_WIDTH,
  imageHeight: process.env.IMAGE_HEIGHT,
};

// Xác thực và xuất cấu hình
let validatedConfig;

try {
  validatedConfig = configSchema.parse(config);
} catch (error) {
  console.error("Lỗi cấu hình:", error);
  process.exit(1);
}

// Đảm bảo đường dẫn tuyệt đối cho các thư mục
const absoluteUploadDir = path.resolve(validatedConfig.uploadDir);
const absoluteBackupDir = path.resolve(validatedConfig.backupDir);

const appConfig = {
  ...validatedConfig,
  uploadDir: absoluteUploadDir,
  backupDir: absoluteBackupDir,
  allowedMimeTypes: ["image/jpeg", "image/png"],
  allowedExtensions: [".jpg", ".jpeg", ".png"],
};

export default appConfig;
