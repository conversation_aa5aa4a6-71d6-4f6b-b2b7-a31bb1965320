# Image Storage Server

A Node.js server that provides API for storing images by folder (album), optimizes images during upload, creates public display links, and includes a simple management interface using HTML + TailwindCSS.

## Features

- Album-based image organization
- Image optimization (WebP and AVIF formats)
- Thumbnail generation
- Public image links
- Simple admin interface
- Rate limiting and security features

## Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file in the root directory with the following variables (or use the defaults):

```
PORT=3000
# Maximum file size (5MB)
MAX_FILE_SIZE=5242880
# Default quality settings
DEFAULT_QUALITY=80
DEFAULT_THUMB_QUALITY=75
# Size limits
MAX_WIDTH=1920
THUMB_WIDTH=300
# Remove metadata from images by default
KEEP_METADATA=false
# Rate limiting (requests per 15 minutes)
RATE_LIMIT=100
# Allow origins, separate with commas for multiple
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

## Running the server

Development mode (with auto-restart):

```bash
npm run dev
```

Production mode:

```bash
npm start
```

Or with Docker:

```bash
docker build -t image-server .
docker run -p 3000:3000 -e PORT=3000 image-server
```

## API Endpoints

### Albums

- `GET /api/albums` - List all albums
- `POST /api/albums` - Create a new album
  - Body: `{ "name": "album-name" }`
- `DELETE /api/albums/:album` - Delete an album and all its images
- `GET /api/albums/:album` - List images in an album

### Images

- `POST /api/albums/:album/images` - Upload an image to an album
  - Form data: `image` (file)
  - Query parameters (optional):
    - `quality` (1-100, default: 80)
    - `width` (max width in pixels, default: 1920)
    - `format` ('webp' or 'avif', default: 'webp')
- `DELETE /api/albums/:album/:id` - Delete an image

### Other

- `GET /healthz` - Health check endpoint
- `GET /` - Admin interface

## Admin Interface

Access the admin interface by navigating to `http://localhost:3000` in your browser. The interface allows you to:

1. Create and delete albums
2. Upload images to albums
3. View and delete images
4. Get public links to images

## Security Features

- CORS protection
- Rate limiting
- Helmet for HTTP header security
- Input validation and sanitization
- File type checking

## License

MIT
