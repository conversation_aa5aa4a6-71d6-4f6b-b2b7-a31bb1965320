# Image Management Server

Hệ thống quản lý hình ảnh toàn diện xây dựng trên Node.js và Express với TypeScript.

## Tính năng

- **Tải lên hình ảnh**: Hỗ trợ định dạng JPG và PNG với giới hạn kích thước 5MB
- **Tối ưu hóa hình ảnh**: Tự động điều chỉnh kích thước và nén hình ảnh để cải thiện hiệu suất
- **Quản lý thư mục**: Tổ chức hình ảnh trong cấu trúc thư mục phân cấp
- **Sao lưu tự động**: Bảo vệ dữ liệu với tính năng sao lưu tự động hoặc thủ công
- **Quản lý URL**: Tạo và sao chép URL hình ảnh nhanh chóng vào clipboard
- **X<PERSON><PERSON> an toàn**: <PERSON><PERSON><PERSON> cơ chế xác nhận để tránh mất dữ liệu do xóa nhầm
- **Giao diện người dùng**: Giao diện thân thiện với Tailwind CSS và EJS templates

## Công nghệ sử dụng

- **Backend**: Node.js, Express, TypeScript
- **Xử lý hình ảnh**: Multer, Sharp
- **Xác thực dữ liệu**: Zod
- **Frontend**: EJS templates, Tailwind CSS
- **Tiện ích**: Lodash, node-cron
- **Ghi log**: Winston

## Yêu cầu hệ thống

- Node.js (>= 14.x)
- npm hoặc yarn

## Cài đặt

### Bước 1: Clone repository

```bash
git clone <repository-url>
cd image-management-server
```

### Bước 2: Cài đặt dependencies

```bash
npm install
```

hoặc sử dụng yarn:

```bash
yarn
```

### Bước 3: Cấu hình biến môi trường

Tạo file `.env` từ mẫu `.env.example`:

```bash
cp .env.example .env
```

Chỉnh sửa các giá trị trong file `.env` nếu cần.

### Bước 4: Build và khởi động server

```bash
# Build TypeScript
npm run build

# Khởi động server
npm start
```

Hoặc để chạy trong chế độ phát triển với hot-reload:

```bash
npm run dev
```

Server sẽ được khởi chạy tại `http://localhost:3000` (hoặc port được cấu hình trong .env)

## Cấu trúc dự án

```
.
├── src/                    # Mã nguồn
│   ├── config/             # Cấu hình ứng dụng
│   ├── controllers/        # Controllers xử lý request
│   ├── middleware/         # Middleware Express
│   ├── models/             # Định nghĩa model
│   ├── public/             # Tệp tĩnh (CSS, JS, uploads)
│   ├── routes/             # Định nghĩa routes
│   ├── services/           # Logic nghiệp vụ
│   ├── utils/              # Tiện ích
│   ├── views/              # EJS templates
│   ├── app.ts              # Cấu hình Express
│   └── index.ts            # Điểm vào ứng dụng
├── dist/                   # Mã đã biên dịch (tạo bởi TypeScript)
├── logs/                   # Log ứng dụng
├── backup-images/          # Thư mục sao lưu
├── tsconfig.json           # Cấu hình TypeScript
├── package.json            # Dependencies và scripts
├── .env                    # Biến môi trường
└── README.md               # Tài liệu
```

## API Endpoints

### Quản lý hình ảnh

- `POST /api/images/upload` - Tải lên hình ảnh mới
- `GET /api/images` - Lấy tất cả hình ảnh
- `GET /api/images/:id` - Lấy thông tin hình ảnh theo ID
- `GET /api/images/folder/:folderName` - Lấy hình ảnh theo thư mục
- `PUT /api/images/:id/rename` - Đổi tên hình ảnh
- `PUT /api/images/:id/move` - Di chuyển hình ảnh sang thư mục khác
- `DELETE /api/images/:id` - Xóa hình ảnh
- `POST /api/images/scan` - Quét và đồng bộ thư mục uploads
- `GET /api/images/:id/url` - Lấy URL hình ảnh

### Quản lý thư mục

- `POST /api/folders` - Tạo thư mục mới
- `GET /api/folders` - Lấy tất cả thư mục
- `GET /api/folders/:folderName` - Lấy thông tin chi tiết thư mục
- `GET /api/folders/:folderName/images` - Lấy hình ảnh trong thư mục
- `PUT /api/folders/:folderName/rename` - Đổi tên thư mục
- `DELETE /api/folders/:folderName` - Xóa thư mục
- `POST /api/folders/:folderName/move-image` - Di chuyển hình ảnh giữa các thư mục

### Quản lý sao lưu

- `POST /api/backups/manual` - Thực hiện sao lưu thủ công
- `POST /api/backups/schedule` - Lên lịch sao lưu tự động
- `POST /api/backups/stop-schedule` - Dừng lịch trình sao lưu tự động
- `GET /api/backups` - Lấy danh sách các bản sao lưu
- `DELETE /api/backups` - Xóa một bản sao lưu

## Đóng góp

Vui lòng đọc [CONTRIBUTING.md](CONTRIBUTING.md) để biết chi tiết về quy trình đóng góp mã nguồn.

## Giấy phép

Dự án được phân phối dưới giấy phép [ISC](LICENSE).
