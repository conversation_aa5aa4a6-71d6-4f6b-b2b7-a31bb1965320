import { Router } from "express";
import imageController from "../controllers/image.controller";
import upload from "../middleware/file-upload.middleware";

const router = Router();

// Tải lên hình ảnh
router.post("/upload", upload.single("image"), imageController.uploadImage);

// L<PERSON>y tất cả hình ảnh
router.get("/", imageController.getAllImages);

// Quét thư mục tải lên để đồng bộ hóa
router.post("/scan", imageController.scanUploadDirectory);

// L<PERSON>y hình ảnh theo ID
router.get("/:id", imageController.getImageById);

// Lấy URL hình ảnh để sao chép vào clipboard
router.get("/:id/url", imageController.getImageUrl);

// L<PERSON>y hình ảnh theo thư mục
router.get("/folder/:folderName", imageController.getImagesByFolder);

// Đ<PERSON>i tên hình ảnh
router.put("/:id/rename", imageController.renameImage);

// Di chuyển hình ảnh sang thư mục khác
router.put("/:id/move", imageController.moveImage);

// Xóa hình ảnh
router.delete("/:id", imageController.deleteImage);

export default router;
