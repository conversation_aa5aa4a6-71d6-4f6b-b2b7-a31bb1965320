import app from "./app";
import http from "http";
import config from "./config/config";
import { logInfo, logError } from "./utils/logger";
import fs from "fs";
import path from "path";
import backupService from "./services/backup.service";

/**
 * Đ<PERSON><PERSON> bảo các thư mục cần thiết tồn tại
 */
function ensureRequiredDirectoriesExist(): void {
  const requiredDirs = [
    "logs",
    config.uploadDir,
    path.join(config.uploadDir, "default"),
    config.backupDir,
  ];

  for (const dir of requiredDirs) {
    if (!fs.existsSync(dir)) {
      try {
        fs.mkdirSync(dir, { recursive: true });
        logInfo(`Đã tạo thư mục: ${dir}`);
      } catch (error) {
        logError(`Không thể tạo thư mục: ${dir}`, error);
      }
    }
  }
}

/**
 * Khởi tạo máy chủ HTTP
 */
function createServer(): http.Server {
  return http.createServer(app);
}

/**
 * Khởi động ứng dụng
 */
async function startServer(): Promise<void> {
  try {
    // Đảm bảo các thư mục cần thiết tồn tại
    ensureRequiredDirectoriesExist();

    const server = createServer();
    const port = config.port;

    server.listen(port, () => {
      logInfo(`Máy chủ đang chạy tại http://localhost:${port}`);

      // Khởi tạo lịch trình sao lưu tự động
      backupService.initScheduledBackup();
      logInfo(
        `Đã lên lịch sao lưu tự động với biểu thức: ${backupService.getCronExpression()}`
      );
    });

    // Xử lý tắt máy chủ một cách an toàn
    const gracefulShutdown = async (signal: string): Promise<void> => {
      logInfo(`Nhận được tín hiệu: ${signal}, đang đóng máy chủ...`);

      server.close(() => {
        logInfo("Máy chủ đã đóng thành công.");
        process.exit(0);
      });

      // Nếu máy chủ không đóng trong vòng 10 giây, tắt cưỡng bức
      setTimeout(() => {
        logError("Quá thời gian đóng máy chủ, tắt cưỡng bức.");
        process.exit(1);
      }, 10000);
    };

    // Xử lý các tín hiệu kết thúc
    process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
    process.on("SIGINT", () => gracefulShutdown("SIGINT"));

    // Xử lý lỗi không bắt được
    process.on("uncaughtException", (error) => {
      logError("Lỗi không bắt được:", error);
      // Không đóng máy chủ để đảm bảo khả năng phục hồi
    });

    process.on("unhandledRejection", (reason) => {
      logError("Promise rejection không được xử lý:", reason);
      // Không đóng máy chủ để đảm bảo khả năng phục hồi
    });
  } catch (error) {
    logError("Lỗi khi khởi động máy chủ:", error);
    process.exit(1);
  }
}

// Khởi động ứng dụng
startServer().catch((error) => {
  logError("Lỗi không mong đợi khi khởi động:", error);
  process.exit(1);
});
