import "dotenv/config";
import path from "path";
import fs from "fs";
import express from "express";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import rateLimit from "express-rate-limit";
import morgan from "morgan";
import multer from "multer";
import sharp from "sharp";
import { nanoid } from "nanoid";
import { fileURLToPath } from "url";
import {
  ensureDirSync,
  listDirsSync,
  listFilesSync,
  tryUnlink,
} from "./utils/fs.js";
import {
  sanitizeAlbum,
  allowedMime,
  validateId,
  validateFormat,
  validateNumber,
} from "./utils/validate.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PORT = process.env.PORT || 3000;
const ROOT = path.join(__dirname, "..");
const PUBLIC_DIR = path.join(ROOT, "public");
const ALBUMS_DIR = path.join(PUBLIC_DIR, "albums");
const ADMIN_DIR = path.join(PUBLIC_DIR, "admin");
const MAX_FILE_SIZE = Number(process.env.MAX_FILE_SIZE || 5 * 1024 * 1024);
const DEFAULT_QUALITY = Number(process.env.DEFAULT_QUALITY || 80);
const DEFAULT_THUMB_QUALITY = Number(process.env.DEFAULT_THUMB_QUALITY || 75);
const MAX_WIDTH = Number(process.env.MAX_WIDTH || 1920);
const THUMB_WIDTH = Number(process.env.THUMB_WIDTH || 300);
const KEEP_METADATA = process.env.KEEP_METADATA === "true";

ensureDirSync(PUBLIC_DIR);
ensureDirSync(ALBUMS_DIR);
ensureDirSync(ADMIN_DIR);

const app = express();

// Security + utils
app.use(helmet());
app.use(compression());
app.use(morgan("combined"));

// Global error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: "Something went wrong!" });
});

// CORS: allow specific origins only
const origins = (process.env.ALLOWED_ORIGINS || "")
  .split(",")
  .map((o) => o.trim())
  .filter(Boolean);
app.use(
  cors({
    origin: function (origin, cb) {
      // Allow same-origin/no-origin (curl, Postman) and whitelisted origins
      if (!origin || origins.includes(origin)) return cb(null, true);
      cb(new Error("Not allowed by CORS"));
    },
  })
);

// Rate limit API
app.use(
  "/api",
  rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: Number(process.env.RATE_LIMIT || 100),
  })
);

// Static files
app.use(
  "/public",
  express.static(PUBLIC_DIR, { maxAge: "30d", immutable: true })
);

// Admin page
app.get("/", (req, res) => {
  res.sendFile(path.join(ADMIN_DIR, "index.html"));
});

// Multer in-memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: MAX_FILE_SIZE },
  fileFilter: (req, file, cb) => {
    // Basic MIME filter
    if (!allowedMime.has(file.mimetype))
      return cb(new Error("Unsupported file type"));
    cb(null, true);
  },
});

// Helpers
function albumPath(name) {
  return path.join(ALBUMS_DIR, name);
}

// Update buildUrls function to handle different formats
function buildUrls(req, album, id) {
  // Detect file format by checking if the file exists
  const webpExists = fs.existsSync(path.join(ALBUMS_DIR, album, `${id}.webp`));
  const avifExists = fs.existsSync(path.join(ALBUMS_DIR, album, `${id}.avif`));

  const format = webpExists ? "webp" : avifExists ? "avif" : "webp"; // Default to webp if neither exists

  const base = `${req.protocol}://${req.get("host")}/public/albums/${album}`;
  return {
    image: `${base}/${id}.${format}`,
    thumb: `${base}/thumb_${id}.${format}`,
  };
}

// Add API request validation middleware
const validateAlbumParam = (req, res, next) => {
  const album = sanitizeAlbum(req.params.album);
  if (!album) {
    return res.status(400).json({ error: "Invalid album name" });
  }
  req.validatedAlbum = album;
  next();
};

// Add album existence middleware
const checkAlbumExists = (req, res, next) => {
  const album = req.validatedAlbum;
  const dir = albumPath(album);
  if (!fs.existsSync(dir)) {
    return res.status(404).json({ error: "Album not found" });
  }
  next();
};

// Routes
// List albums
app.get("/api/albums", (req, res) => {
  try {
    res.json({ albums: listDirsSync(ALBUMS_DIR) });
  } catch (err) {
    console.error("Error listing albums:", err);
    res.status(500).json({ error: "Failed to list albums" });
  }
});

// Create album
app.post("/api/albums", express.json(), (req, res) => {
  const { name } = req.body;
  const album = sanitizeAlbum(name);
  if (!album) return res.status(400).json({ error: "Invalid album name" });

  try {
    const dir = albumPath(album);
    if (fs.existsSync(dir)) {
      return res.status(409).json({ error: "Album already exists" });
    }

    ensureDirSync(dir);
    return res.status(201).json({ name: album });
  } catch (err) {
    console.error(err);
    return res.status(500).json({ error: "Failed to create album" });
  }
});

// Delete album
app.delete("/api/albums/:album", validateAlbumParam, async (req, res) => {
  const album = req.validatedAlbum;

  try {
    const dir = albumPath(album);
    if (!fs.existsSync(dir)) {
      return res.status(404).json({ error: "Album not found" });
    }

    // Remove all files in the album directory
    const files = fs.readdirSync(dir);
    for (const file of files) {
      await fs.promises.unlink(path.join(dir, file));
    }

    // Remove the directory itself
    await fs.promises.rmdir(dir);

    return res.json({ ok: true });
  } catch (err) {
    console.error(err);
    return res.status(500).json({ error: "Failed to delete album" });
  }
});

// List images in an album
app.get(
  "/api/albums/:album",
  validateAlbumParam,
  checkAlbumExists,
  (req, res) => {
    const album = req.validatedAlbum;

    try {
      const dir = albumPath(album);
      const files = listFilesSync(dir)
        .filter(
          (f) =>
            (f.endsWith(".webp") || f.endsWith(".avif")) &&
            !f.startsWith("thumb_")
        )
        .map((f) => {
          const id = path.parse(f).name; // without extension
          return buildUrls(req, album, id);
        });
      res.json({ album, images: files });
    } catch (err) {
      console.error("Error listing images:", err);
      res.status(500).json({ error: "Failed to list images" });
    }
  }
);

// Upload endpoint
app.post(
  "/api/albums/:album/images",
  validateAlbumParam,
  upload.single("image"),
  async (req, res) => {
    try {
      const album = req.validatedAlbum;
      if (!req.file) return res.status(400).json({ error: "No file uploaded" });

      // Get optimization options from request and validate them
      const quality = validateNumber(
        req.query.quality,
        1,
        100,
        DEFAULT_QUALITY
      );
      const maxWidth = validateNumber(req.query.width, 100, 4000, MAX_WIDTH);
      const format = req.query.format || "webp";

      // Validate format
      if (!validateFormat(format)) {
        return res
          .status(400)
          .json({ error: "Invalid output format. Use webp or avif" });
      }

      // Validate actual content is an image via sharp
      let meta;
      try {
        meta = await sharp(req.file.buffer).metadata();
        if (!meta || !meta.width || !meta.height) {
          return res.status(400).json({ error: "Invalid image content" });
        }
      } catch (err) {
        return res
          .status(400)
          .json({ error: "Invalid image content: " + err.message });
      }

      const id = nanoid(12);
      const dir = albumPath(album);
      ensureDirSync(dir);

      const imgPath = path.join(dir, `${id}.${format}`);
      const thumbPath = path.join(dir, `thumb_${id}.${format}`);

      // Configure sharp for image processing
      try {
        let sharpImage = sharp(req.file.buffer).rotate(); // auto-orient from EXIF

        // Remove metadata unless explicitly kept
        if (!KEEP_METADATA) {
          sharpImage = sharpImage.withMetadata(false);
        }

        // Resize the image
        sharpImage = sharpImage.resize({
          width: Math.min(meta.width, maxWidth),
          withoutEnlargement: true,
        });

        // Format options
        const formatOptions = {
          quality: quality,
        };

        // Save optimized image in requested format
        if (format === "webp") {
          await sharpImage.webp(formatOptions).toFile(imgPath);
        } else {
          await sharpImage.avif(formatOptions).toFile(imgPath);
        }
      } catch (err) {
        console.error("Error processing main image:", err);
        return res
          .status(400)
          .json({ error: "Error processing image: " + err.message });
      }

      // Create thumbnail
      try {
        let thumbnail = sharp(req.file.buffer).rotate();

        // Remove metadata from thumbnail
        if (!KEEP_METADATA) {
          thumbnail = thumbnail.withMetadata(false);
        }

        thumbnail = thumbnail.resize({
          width: THUMB_WIDTH,
          withoutEnlargement: true,
        });

        const thumbFormatOptions = {
          quality: DEFAULT_THUMB_QUALITY,
        };

        // Save thumbnail in the same format
        if (format === "webp") {
          await thumbnail.webp(thumbFormatOptions).toFile(thumbPath);
        } else {
          await thumbnail.avif(thumbFormatOptions).toFile(thumbPath);
        }
      } catch (err) {
        // If thumbnail creation fails, try to clean up the main image
        tryUnlink(imgPath);
        console.error("Error creating thumbnail:", err);
        return res
          .status(400)
          .json({ error: "Error creating thumbnail: " + err.message });
      }

      return res.status(201).json({
        id,
        album,
        format,
        width: Math.min(meta.width, maxWidth),
        height: meta.height * (Math.min(meta.width, maxWidth) / meta.width), // Calculate proportional height
        originalWidth: meta.width,
        originalHeight: meta.height,
        originalFormat: meta.format,
        quality,
        ...buildUrls(req, album, id),
      });
    } catch (err) {
      console.error("Upload error:", err);
      return res.status(500).json({ error: err.message || "Upload failed" });
    }
  }
);

// Delete image
app.delete(
  "/api/albums/:album/:id",
  validateAlbumParam,
  checkAlbumExists,
  (req, res) => {
    const album = req.validatedAlbum;
    const id = req.params.id;

    if (!validateId(id)) {
      return res.status(400).json({ error: "Invalid image ID" });
    }

    try {
      const dir = albumPath(album);
      let deleted = false;

      // Check and delete both webp and avif versions
      ["webp", "avif"].forEach((format) => {
        const imgPath = path.join(dir, `${id}.${format}`);
        const thumbPath = path.join(dir, `thumb_${id}.${format}`);

        if (fs.existsSync(imgPath)) {
          fs.unlinkSync(imgPath);
          deleted = true;
        }

        if (fs.existsSync(thumbPath)) {
          fs.unlinkSync(thumbPath);
        }
      });

      if (!deleted) {
        return res.status(404).json({ error: "Image not found" });
      }

      return res.json({ ok: true });
    } catch (err) {
      console.error("Error deleting image:", err);
      return res.status(500).json({ error: "Failed to delete image" });
    }
  }
);

// Health
app.get("/healthz", (_req, res) => res.json({ ok: true }));

// Handle 404
app.use((req, res) => {
  res.status(404).json({ error: "Not Found" });
});

app.listen(PORT, () => {
  console.log(`Server listening on http://localhost:${PORT}`);
});
