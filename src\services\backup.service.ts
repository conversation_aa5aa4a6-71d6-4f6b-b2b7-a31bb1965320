import fs from "fs";
import path from "path";
import { promisify } from "util";
import node<PERSON>ron from "node-cron";
import config from "../config/config";
import { logError, logInfo } from "../utils/logger";

// <PERSON>yển đổi các hàm callback thành Promise
const mkdir = promisify(fs.mkdir);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const copyFile = promisify(fs.copyFile);

/**
 * Interface cho kết quả sao lưu
 */
interface BackupResult {
  success: boolean;
  backupPath?: string;
  timestamp?: Date;
  totalFiles?: number;
  error?: string;
}

/**
 * Lớp dịch vụ sao lưu
 */
class BackupService {
  private readonly sourceDir: string;
  private readonly backupDir: string;
  private cronJob: nodeCron.ScheduledTask | null;
  private currentCronExpression: string;

  constructor() {
    this.sourceDir = config.uploadDir;
    this.backupDir = config.backupDir;
    this.cronJob = null;
    this.currentCronExpression = config.backupInterval;

    // <PERSON><PERSON><PERSON> b<PERSON>o thư mục sao lưu tồn tại
    this.ensureBackupDirectoryExists();
  }

  /**
   * Đảm bảo thư mục sao lưu tồn tại
   */
  private async ensureBackupDirectoryExists(): Promise<void> {
    try {
      await mkdir(this.backupDir, { recursive: true });
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code !== "EEXIST") {
        logError("Lỗi khi tạo thư mục sao lưu", error);
        throw error;
      }
    }
  }

  /**
   * Khởi tạo tác vụ sao lưu theo lịch trình
   * @param cronExpression Biểu thức cron định dạng lịch trình
   */
  public initScheduledBackup(
    cronExpression: string = config.backupInterval
  ): void {
    if (this.cronJob) {
      this.cronJob.stop();
    }

    try {
      // Xác thực biểu thức cron
      if (!nodeCron.validate(cronExpression)) {
        throw new Error(`Biểu thức cron không hợp lệ: ${cronExpression}`);
      }

      this.currentCronExpression = cronExpression;

      this.cronJob = nodeCron.schedule(cronExpression, async () => {
        try {
          const result = await this.performBackup();
          if (result.success) {
            logInfo(
              `Sao lưu tự động hoàn tất. Đường dẫn: ${result.backupPath}`
            );
          } else {
            logError(`Sao lưu tự động thất bại: ${result.error}`);
          }
        } catch (error) {
          logError("Lỗi không mong đợi trong lịch trình sao lưu", error);
        }
      });

      logInfo(`Đã lên lịch sao lưu tự động với biểu thức: ${cronExpression}`);
    } catch (error) {
      logError("Lỗi khi thiết lập lịch trình sao lưu", error);
      throw error;
    }
  }

  /**
   * Lấy biểu thức cron hiện tại
   * @returns Biểu thức cron đang được sử dụng
   */
  public getCronExpression(): string {
    return this.currentCronExpression;
  }

  /**
   * Dừng tác vụ sao lưu đã lên lịch
   */
  public stopScheduledBackup(): void {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
      logInfo("Đã dừng lịch trình sao lưu tự động");
    }
  }

  /**
   * Thực hiện sao lưu thủ công
   * @returns Kết quả sao lưu
   */
  public async performBackup(): Promise<BackupResult> {
    try {
      await this.ensureBackupDirectoryExists();

      // Tạo thư mục sao lưu với timestamp
      const timestamp = new Date();
      const folderName = `backup-${timestamp
        .toISOString()
        .replace(/:/g, "-")
        .replace(/\..+/, "")}`;
      const backupPath = path.join(this.backupDir, folderName);

      await mkdir(backupPath, { recursive: true });

      // Đọc cấu trúc thư mục nguồn
      const folders = await this.getFolders(this.sourceDir);

      // Tạo các thư mục tương ứng trong thư mục sao lưu
      for (const folder of folders) {
        const relativePath = path.relative(this.sourceDir, folder);
        const backupFolderPath = path.join(backupPath, relativePath);
        await mkdir(backupFolderPath, { recursive: true });
      }

      // Sao chép tất cả tệp từ thư mục nguồn sang thư mục sao lưu
      let totalFiles = 0;
      for (const folder of folders) {
        const relativePath = path.relative(this.sourceDir, folder);
        const backupFolderPath = path.join(backupPath, relativePath);

        // Sao chép các tệp trong thư mục
        const files = await readdir(folder);
        for (const file of files) {
          const sourcePath = path.join(folder, file);
          const targetPath = path.join(backupFolderPath, file);

          const fileStat = await stat(sourcePath);
          if (fileStat.isFile()) {
            await copyFile(sourcePath, targetPath);
            totalFiles++;
          }
        }
      }

      // Tạo tệp metadata
      const metadata = {
        timestamp: timestamp.toISOString(),
        totalFiles,
        sourcePath: this.sourceDir,
      };

      fs.writeFileSync(
        path.join(backupPath, "metadata.json"),
        JSON.stringify(metadata, null, 2)
      );

      logInfo(
        `Sao lưu thành công. Đường dẫn: ${backupPath}, Số tệp: ${totalFiles}`
      );

      return {
        success: true,
        backupPath,
        timestamp,
        totalFiles,
      };
    } catch (error) {
      logError("Lỗi trong quá trình sao lưu", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      };
    }
  }

  /**
   * Lấy tất cả các thư mục (bao gồm thư mục con) từ một thư mục gốc
   * @param rootDir Thư mục gốc
   * @returns Danh sách đường dẫn thư mục
   */
  private async getFolders(rootDir: string): Promise<string[]> {
    const results: string[] = [];
    results.push(rootDir);

    try {
      const items = await readdir(rootDir);

      for (const item of items) {
        const fullPath = path.join(rootDir, item);
        const itemStat = await stat(fullPath);

        if (itemStat.isDirectory()) {
          // Đệ quy để lấy tất cả thư mục con
          const subfolders = await this.getFolders(fullPath);
          results.push(...subfolders);
        }
      }
    } catch (error) {
      logError(`Lỗi khi đọc thư mục: ${rootDir}`, error);
      // Tiếp tục với các thư mục đã tìm thấy
    }

    return results;
  }

  /**
   * Lấy danh sách các bản sao lưu
   */
  public async getBackupList(): Promise<
    Array<{ name: string; timestamp: Date; totalFiles: number }>
  > {
    try {
      const backups = [];
      const items = await readdir(this.backupDir);

      for (const item of items) {
        if (item.startsWith("backup-")) {
          const fullPath = path.join(this.backupDir, item);
          const itemStat = await stat(fullPath);

          if (itemStat.isDirectory()) {
            try {
              // Đọc tệp metadata nếu có
              const metadataPath = path.join(fullPath, "metadata.json");
              if (fs.existsSync(metadataPath)) {
                const metadata = JSON.parse(
                  fs.readFileSync(metadataPath, "utf8")
                );
                backups.push({
                  name: item,
                  timestamp: new Date(metadata.timestamp),
                  totalFiles: metadata.totalFiles,
                });
              } else {
                backups.push({
                  name: item,
                  timestamp: itemStat.birthtime,
                  totalFiles: 0,
                });
              }
            } catch (metadataError) {
              logError(
                `Lỗi khi đọc metadata cho bản sao lưu: ${item}`,
                metadataError
              );
              backups.push({
                name: item,
                timestamp: itemStat.birthtime,
                totalFiles: 0,
              });
            }
          }
        }
      }

      // Sắp xếp theo thời gian giảm dần (mới nhất trước)
      return backups.sort(
        (a, b) => b.timestamp.getTime() - a.timestamp.getTime()
      );
    } catch (error) {
      logError("Lỗi khi lấy danh sách bản sao lưu", error);
      throw error;
    }
  }

  /**
   * Xóa một bản sao lưu
   * @param backupName Tên thư mục sao lưu
   */
  public async deleteBackup(backupName: string): Promise<boolean> {
    try {
      if (!backupName.startsWith("backup-")) {
        throw new Error("Tên bản sao lưu không hợp lệ");
      }

      const backupPath = path.join(this.backupDir, backupName);

      // Kiểm tra xem thư mục có tồn tại không
      try {
        await stat(backupPath);
      } catch (error) {
        throw new Error(`Bản sao lưu '${backupName}' không tồn tại`);
      }

      // Xóa đệ quy thư mục
      await this.deleteFolder(backupPath);

      logInfo(`Đã xóa bản sao lưu: ${backupPath}`);
      return true;
    } catch (error) {
      logError(`Lỗi khi xóa bản sao lưu: ${backupName}`, error);
      throw error;
    }
  }

  /**
   * Xóa đệ quy thư mục
   * @param folderPath Đường dẫn thư mục
   */
  private async deleteFolder(folderPath: string): Promise<void> {
    try {
      const files = await readdir(folderPath);

      for (const file of files) {
        const curPath = path.join(folderPath, file);
        const fileStat = await stat(curPath);

        if (fileStat.isDirectory()) {
          // Đệ quy để xóa thư mục con
          await this.deleteFolder(curPath);
        } else {
          // Xóa tệp
          await promisify(fs.unlink)(curPath);
        }
      }

      // Xóa thư mục sau khi tất cả tệp con đã được xóa
      await promisify(fs.rmdir)(folderPath);
    } catch (error) {
      logError(`Lỗi khi xóa thư mục: ${folderPath}`, error);
      throw error;
    }
  }
}

export default new BackupService();
