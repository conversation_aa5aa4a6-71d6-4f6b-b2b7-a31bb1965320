<%- include('layouts/main', { title: 'Image Management Server - T<PERSON>i lên',
pageTitle: 'T<PERSON>i lên hình ảnh' }) %>

<div class="mx-auto max-w-4xl">
  <!-- <PERSON><PERSON> vực kéo thả tệp -->
  <div
    id="drop-area"
    class="mt-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-12 text-center hover:border-primary-500 dark:hover:border-primary-400 transition-colors"
  >
    <input
      type="file"
      id="fileInput"
      class="hidden"
      accept=".jpg,.jpeg,.png"
      multiple
    />
    <div class="space-y-4">
      <i
        class="fas fa-cloud-upload-alt text-6xl text-gray-400 dark:text-gray-500"
      ></i>
      <h3 class="text-xl font-medium text-gray-700 dark:text-gray-300">
        Kéo và thả hình ảnh vào đây
      </h3>
      <p class="text-gray-500 dark:text-gray-400">hoặc</p>
      <button
        type="button"
        id="browseButton"
        class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
      >
        <i class="fas fa-folder-open mr-2"></i> Chọn tệp
      </button>
      <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
        Hỗ trợ: JPG, JPEG, PNG. Kích thước tối đa: 5MB.
      </p>
    </div>
  </div>

  <!-- Thông tin thư mục -->
  <div class="mt-8">
    <label
      for="folderSelect"
      class="block text-sm font-medium text-gray-700 dark:text-gray-300"
    >
      Chọn thư mục lưu trữ:
    </label>
    <div class="mt-1 flex rounded-md shadow-sm">
      <select
        id="folderSelect"
        class="flex-1 focus:ring-primary-500 focus:border-primary-500 block w-full min-w-0 rounded-md sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300"
      >
        <option value="default" selected>Mặc định</option>
        <!-- Danh sách thư mục sẽ được điền bằng JavaScript -->
      </select>
      <button
        type="button"
        id="newFolderBtn"
        class="inline-flex items-center px-4 py-2 ml-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
      >
        <i class="fas fa-folder-plus mr-2"></i> Thư mục mới
      </button>
    </div>
  </div>

  <!-- Danh sách tệp đang tải lên -->
  <div class="mt-8">
    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
      Danh sách tệp
    </h3>
    <div id="file-list" class="mt-3 space-y-4">
      <!-- Hàng mẫu, sẽ được điền bởi JavaScript -->
    </div>
  </div>

  <!-- Nút tải lên -->
  <div class="mt-8 flex justify-end">
    <button
      id="uploadBtn"
      disabled
      type="button"
      class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
    >
      <i class="fas fa-upload mr-2"></i> Tải lên tất cả
    </button>
  </div>
</div>

<!-- Modal thư mục mới -->
<div id="newFolderModal" class="fixed inset-0 z-10 hidden overflow-y-auto">
  <div
    class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
  >
    <div class="fixed inset-0 transition-opacity" aria-hidden="true">
      <div
        class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"
      ></div>
    </div>
    <span
      class="hidden sm:inline-block sm:align-middle sm:h-screen"
      aria-hidden="true"
      >&#8203;</span
    >
    <div
      class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
    >
      <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <div
            class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10"
          >
            <i class="fas fa-folder-plus text-blue-600 dark:text-blue-300"></i>
          </div>
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
            <h3
              class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100"
              id="modal-title"
            >
              Tạo thư mục mới
            </h3>
            <div class="mt-2">
              <div class="mt-1">
                <input
                  type="text"
                  name="folderName"
                  id="folderName"
                  class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 rounded-md"
                  placeholder="Tên thư mục"
                />
              </div>
              <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Nhập tên thư mục để tổ chức hình ảnh của bạn.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div
        class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"
      >
        <button
          type="button"
          id="createFolderBtn"
          class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm"
        >
          Tạo
        </button>
        <button
          type="button"
          id="cancelFolderBtn"
          class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
        >
          Hủy
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  // Script cho trang tải lên
  document.addEventListener("DOMContentLoaded", function () {
    // Các biến toàn cục
    const fileInput = document.getElementById("fileInput");
    const dropArea = document.getElementById("drop-area");
    const browseButton = document.getElementById("browseButton");
    const fileList = document.getElementById("file-list");
    const uploadButton = document.getElementById("uploadBtn");
    const folderSelect = document.getElementById("folderSelect");
    const newFolderBtn = document.getElementById("newFolderBtn");
    const newFolderModal = document.getElementById("newFolderModal");
    const folderNameInput = document.getElementById("folderName");
    const createFolderBtn = document.getElementById("createFolderBtn");
    const cancelFolderBtn = document.getElementById("cancelFolderBtn");

    // Mảng lưu trữ danh sách tệp
    let files = [];

    // Tải danh sách thư mục
    loadFolders();

    // Xử lý sự kiện kéo và thả
    ["dragenter", "dragover", "dragleave", "drop"].forEach((eventName) => {
      dropArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
      e.preventDefault();
      e.stopPropagation();
    }

    ["dragenter", "dragover"].forEach((eventName) => {
      dropArea.addEventListener(eventName, highlight, false);
    });

    ["dragleave", "drop"].forEach((eventName) => {
      dropArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
      dropArea.classList.add("border-primary-500", "dark:border-primary-400");
    }

    function unhighlight() {
      dropArea.classList.remove(
        "border-primary-500",
        "dark:border-primary-400"
      );
    }

    // Xử lý sự kiện kéo thả tệp
    dropArea.addEventListener("drop", handleDrop, false);

    function handleDrop(e) {
      const dt = e.dataTransfer;
      const droppedFiles = dt.files;
      handleFiles(droppedFiles);
    }

    // Xử lý sự kiện chọn tệp
    browseButton.addEventListener("click", () => {
      fileInput.click();
    });

    fileInput.addEventListener("change", () => {
      handleFiles(fileInput.files);
    });

    // Xử lý tệp được chọn
    function handleFiles(selectedFiles) {
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];

        // Kiểm tra loại tệp
        if (!file.type.match("image/jpeg") && !file.type.match("image/png")) {
          showNotification("Chỉ chấp nhận tệp JPG và PNG.", "error");
          continue;
        }

        // Kiểm tra kích thước tệp
        if (file.size > 5 * 1024 * 1024) {
          // 5MB
          showNotification("Kích thước tệp vượt quá giới hạn 5MB.", "error");
          continue;
        }

        // Thêm tệp vào mảng
        const fileId = `file-${Date.now()}-${i}`;
        files.push({
          id: fileId,
          file: file,
          preview: null,
          status: "pending", // pending, uploading, success, error
        });

        // Hiển thị tệp trong danh sách
        addFileToList(fileId, file);

        // Tạo xem trước
        createPreview(fileId, file);
      }

      // Kích hoạt nút tải lên nếu có tệp
      uploadButton.disabled = files.length === 0;
    }

    // Thêm tệp vào danh sách hiển thị
    function addFileToList(fileId, file) {
      const fileItem = document.createElement("div");
      fileItem.id = fileId;
      fileItem.className =
        "flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow";

      fileItem.innerHTML = `
        <div class="w-16 h-16 mr-4 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center overflow-hidden">
          <img id="${fileId}-preview" class="object-cover w-full h-full" src="" alt="Preview">
        </div>
        <div class="flex-1">
          <div class="flex items-center justify-between">
            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">${
              file.name
            }</h4>
            <button type="button" class="text-red-500 hover:text-red-700" onclick="removeFile('${fileId}')">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <p class="text-xs text-gray-500 dark:text-gray-400">${formatFileSize(
            file.size
          )}</p>
          <div class="mt-2 relative pt-1">
            <div id="${fileId}-progress-container" class="flex mb-2 items-center justify-between hidden">
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div id="${fileId}-progress" class="bg-primary-600 h-2 rounded-full" style="width: 0%"></div>
              </div>
              <span id="${fileId}-progress-text" class="text-xs font-semibold inline-block text-primary-600 dark:text-primary-400 ml-2">0%</span>
            </div>
            <div id="${fileId}-status" class="text-xs text-gray-500 dark:text-gray-400">Chờ tải lên</div>
          </div>
        </div>
      `;

      fileList.appendChild(fileItem);
    }

    // Tạo xem trước hình ảnh
    function createPreview(fileId, file) {
      const reader = new FileReader();
      reader.onload = function (e) {
        const preview = e.target.result;
        document.getElementById(`${fileId}-preview`).src = preview;

        // Lưu xem trước vào mảng
        const fileIndex = files.findIndex((f) => f.id === fileId);
        if (fileIndex !== -1) {
          files[fileIndex].preview = preview;
        }
      };
      reader.readAsDataURL(file);
    }

    // Xử lý tải lên tệp
    uploadButton.addEventListener("click", uploadFiles);

    function uploadFiles() {
      if (files.length === 0) return;

      uploadButton.disabled = true;
      showLoading();

      let uploadedCount = 0;
      let failedCount = 0;

      files.forEach((fileObj, index) => {
        if (fileObj.status === "success") {
          uploadedCount++;
          if (uploadedCount + failedCount === files.length) {
            finishUpload();
          }
          return;
        }

        // Hiển thị tiến trình
        const progressContainer = document.getElementById(
          `${fileObj.id}-progress-container`
        );
        const progressBar = document.getElementById(`${fileObj.id}-progress`);
        const progressText = document.getElementById(
          `${fileObj.id}-progress-text`
        );
        const statusText = document.getElementById(`${fileObj.id}-status`);

        progressContainer.classList.remove("hidden");
        statusText.textContent = "Đang tải lên...";
        fileObj.status = "uploading";

        // Tạo FormData
        const formData = new FormData();
        formData.append("image", fileObj.file);
        formData.append("folder", folderSelect.value);

        // Tạo XMLHttpRequest
        const xhr = new XMLHttpRequest();
        xhr.open("POST", "/api/images/upload", true);

        // Cập nhật tiến trình
        xhr.upload.onprogress = function (e) {
          if (e.lengthComputable) {
            const percentComplete = Math.round((e.loaded / e.total) * 100);
            progressBar.style.width = percentComplete + "%";
            progressText.textContent = percentComplete + "%";
          }
        };

        // Xử lý khi hoàn thành
        xhr.onload = function () {
          if (xhr.status === 201) {
            // Tải lên thành công
            statusText.textContent = "Tải lên thành công";
            statusText.className = "text-xs text-green-500 dark:text-green-400";
            fileObj.status = "success";
            uploadedCount++;
          } else {
            // Tải lên thất bại
            statusText.textContent = "Tải lên thất bại";
            statusText.className = "text-xs text-red-500 dark:text-red-400";
            fileObj.status = "error";
            failedCount++;
          }

          if (uploadedCount + failedCount === files.length) {
            finishUpload();
          }
        };

        // Xử lý lỗi
        xhr.onerror = function () {
          statusText.textContent = "Lỗi kết nối";
          statusText.className = "text-xs text-red-500 dark:text-red-400";
          fileObj.status = "error";
          failedCount++;

          if (uploadedCount + failedCount === files.length) {
            finishUpload();
          }
        };

        // Gửi yêu cầu
        xhr.send(formData);
      });
    }

    // Hoàn thành quá trình tải lên
    function finishUpload() {
      hideLoading();
      uploadButton.disabled = false;

      // Lọc ra các tệp đã tải lên thành công
      const successFiles = files.filter((f) => f.status === "success");

      // Hiển thị thông báo
      const message = `Đã tải lên ${successFiles.length}/${files.length} tệp thành công.`;
      showNotification(
        message,
        successFiles.length === files.length ? "success" : "warning"
      );

      // Xóa các tệp đã tải lên thành công sau 2 giây
      setTimeout(() => {
        successFiles.forEach((f) => {
          removeFileFromList(f.id);
        });

        // Cập nhật mảng tệp
        files = files.filter((f) => f.status !== "success");

        // Cập nhật trạng thái nút tải lên
        uploadButton.disabled = files.length === 0;
      }, 2000);
    }

    // Xóa tệp khỏi danh sách
    window.removeFile = function (fileId) {
      removeFileFromList(fileId);
      files = files.filter((f) => f.id !== fileId);
      uploadButton.disabled = files.length === 0;
    };

    function removeFileFromList(fileId) {
      const fileElement = document.getElementById(fileId);
      if (fileElement) {
        fileElement.classList.add("opacity-0");
        setTimeout(() => {
          if (fileElement.parentNode) {
            fileElement.parentNode.removeChild(fileElement);
          }
        }, 300);
      }
    }

    // Tải danh sách thư mục
    function loadFolders() {
      fetch("/api/folders")
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            // Xóa các tùy chọn hiện tại ngoại trừ mặc định
            while (folderSelect.options.length > 1) {
              folderSelect.remove(1);
            }

            // Thêm các thư mục mới
            data.data.forEach((folder) => {
              const option = document.createElement("option");
              option.value = folder.name;
              option.textContent = folder.name;
              folderSelect.appendChild(option);
            });
          }
        })
        .catch((error) => {
          console.error("Lỗi khi tải danh sách thư mục:", error);
        });
    }

    // Xử lý modal thư mục mới
    newFolderBtn.addEventListener("click", () => {
      newFolderModal.classList.remove("hidden");
      folderNameInput.value = "";
      setTimeout(() => folderNameInput.focus(), 100);
    });

    cancelFolderBtn.addEventListener("click", () => {
      newFolderModal.classList.add("hidden");
    });

    createFolderBtn.addEventListener("click", createNewFolder);

    function createNewFolder() {
      const folderName = folderNameInput.value.trim();

      if (!folderName) {
        showNotification("Vui lòng nhập tên thư mục", "warning");
        return;
      }

      showLoading();

      fetch("/api/folders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name: folderName }),
      })
        .then((response) => response.json())
        .then((data) => {
          hideLoading();
          if (data.success) {
            newFolderModal.classList.add("hidden");
            showNotification(
              `Đã tạo thư mục "${folderName}" thành công`,
              "success"
            );

            // Thêm thư mục mới vào danh sách và chọn nó
            const option = document.createElement("option");
            option.value = data.data.name;
            option.textContent = data.data.name;
            folderSelect.appendChild(option);
            folderSelect.value = data.data.name;
          } else {
            showNotification(data.error || "Không thể tạo thư mục", "error");
          }
        })
        .catch((error) => {
          hideLoading();
          showNotification("Lỗi khi tạo thư mục", "error");
          console.error("Lỗi:", error);
        });
    }

    // Hàm tiện ích định dạng kích thước tệp
    function formatFileSize(bytes) {
      if (bytes === 0) return "0 Bytes";

      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    }
  });
</script>
