import { z } from "zod";

// Schema x<PERSON>c thực cho dữ liệu hình ảnh
export const imageSchema = z.object({
  id: z.string(),
  filename: z.string(),
  originalname: z.string(),
  mimetype: z.string(),
  path: z.string(),
  size: z.number(),
  extension: z.string(),
  width: z.number().optional(),
  height: z.number().optional(),
  folder: z.string().default("default"),
  uploadDate: z.date().default(() => new Date()),
  optimized: z.boolean().default(false),
  optimizedPath: z.string().optional(),
  thumbnailPath: z.string().optional(),
  url: z.string().optional(),
});

// Lấy kiểu TypeScript từ schema Zod
export type ImageModel = z.infer<typeof imageSchema>;

/**
 * Lớp đại diện cho một hình ảnh
 */
class Image implements ImageModel {
  id: string;
  filename: string;
  originalname: string;
  mimetype: string;
  path: string;
  size: number;
  extension: string;
  width?: number;
  height?: number;
  folder: string;
  uploadDate: Date;
  optimized: boolean;
  optimizedPath?: string;
  thumbnailPath?: string;
  url?: string;

  /**
   * Tạo đối tượng hình ảnh mới
   * @param data Dữ liệu hình ảnh
   */
  constructor(data: Partial<ImageModel>) {
    // Thiết lập ID mặc định nếu không được cung cấp
    this.id = data.id || this.generateId();
    this.filename = data.filename || "";
    this.originalname = data.originalname || "";
    this.mimetype = data.mimetype || "";
    this.path = data.path || "";
    this.size = data.size || 0;
    this.extension = data.extension || "";
    this.width = data.width;
    this.height = data.height;
    this.folder = data.folder || "default";
    this.uploadDate = data.uploadDate || new Date();
    this.optimized = data.optimized || false;
    this.optimizedPath = data.optimizedPath;
    this.thumbnailPath = data.thumbnailPath;
    this.url = data.url;

    // Xác thực dữ liệu với schema
    this.validate();
  }

  /**
   * Xác thực dữ liệu đối tượng hình ảnh
   * @throws {Error} Ném lỗi nếu dữ liệu không hợp lệ
   */
  validate(): void {
    try {
      imageSchema.parse(this);
    } catch (error) {
      throw new Error(
        `Dữ liệu hình ảnh không hợp lệ: ${
          error instanceof Error ? error.message : "Lỗi không xác định"
        }`
      );
    }
  }

  /**
   * Tạo ID duy nhất cho hình ảnh
   * @returns ID duy nhất
   */
  private generateId(): string {
    return `img_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Chuyển đối tượng thành JSON
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      filename: this.filename,
      originalname: this.originalname,
      mimetype: this.mimetype,
      path: this.path,
      size: this.size,
      extension: this.extension,
      width: this.width,
      height: this.height,
      folder: this.folder,
      uploadDate: this.uploadDate.toISOString(),
      optimized: this.optimized,
      optimizedPath: this.optimizedPath,
      thumbnailPath: this.thumbnailPath,
      url: this.url,
    };
  }
}

export default Image;
