import { Request, Response } from "express";
import folderService from "../services/folder.service";
import { asyncHandler } from "../middleware/error-handler.middleware";
import { logError } from "../utils/logger";
import { z } from "zod";

/**
 * Schema xác thực cho các yêu cầu liên quan đến thư mục
 */
const createFolderSchema = z.object({
  name: z.string().min(1, "Tên thư mục không được để trống"),
});

const renameFolderSchema = z.object({
  newName: z.string().min(1, "Tên thư mục mới không được để trống"),
});

const deleteFolderSchema = z.object({
  force: z.boolean().optional().default(false),
});

const moveImageSchema = z.object({
  imageName: z.string().min(1, "Tên hình <PERSON>nh không được để trống"),
  targetFolder: z.string().min(1, "<PERSON>h<PERSON> mục đích không được để trống"),
});

/**
 * Controller xử lý các tác vụ liên quan đến thư mục
 */
class FolderController {
  /**
   * Tạo thư mục mới
   * @route POST /api/folders
   */
  public createFolder = asyncHandler(async (req: Request, res: Response) => {
    try {
      // Xác thực dữ liệu đầu vào
      const validatedData = createFolderSchema.parse(req.body);

      // Tạo thư mục
      const folder = await folderService.createFolder(validatedData.name);

      return res.status(201).json({
        success: true,
        data: folder,
        message: "Đã tạo thư mục thành công",
      });
    } catch (error) {
      // Xử lý lỗi xác thực Zod
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: error.errors[0].message,
        });
      }
      throw error;
    }
  });

  /**
   * Lấy tất cả thư mục
   * @route GET /api/folders
   */
  public getAllFolders = asyncHandler(async (_req: Request, res: Response) => {
    const folders = await folderService.getAllFolders();

    return res.status(200).json({
      success: true,
      count: folders.length,
      data: folders,
    });
  });

  /**
   * Lấy thông tin chi tiết về một thư mục
   * @route GET /api/folders/:folderName
   */
  public getFolderInfo = asyncHandler(async (req: Request, res: Response) => {
    const { folderName } = req.params;

    try {
      const folder = await folderService.getFolderInfo(folderName);

      return res.status(200).json({
        success: true,
        data: folder,
      });
    } catch (error) {
      return res.status(404).json({
        success: false,
        error: `Không tìm thấy thư mục: ${folderName}`,
      });
    }
  });

  /**
   * Lấy danh sách hình ảnh trong một thư mục
   * @route GET /api/folders/:folderName/images
   */
  public getImagesInFolder = asyncHandler(
    async (req: Request, res: Response) => {
      const { folderName } = req.params;

      try {
        const images = await folderService.getImagesInFolder(folderName);

        return res.status(200).json({
          success: true,
          folder: folderName,
          count: images.length,
          data: images,
        });
      } catch (error) {
        return res.status(404).json({
          success: false,
          error: `Không tìm thấy thư mục: ${folderName}`,
        });
      }
    }
  );

  /**
   * Đổi tên thư mục
   * @route PUT /api/folders/:folderName/rename
   */
  public renameFolder = asyncHandler(async (req: Request, res: Response) => {
    const { folderName } = req.params;

    try {
      // Xác thực dữ liệu đầu vào
      const validatedData = renameFolderSchema.parse(req.body);

      // Đổi tên thư mục
      const folder = await folderService.renameFolder(
        folderName,
        validatedData.newName
      );

      return res.status(200).json({
        success: true,
        data: folder,
        message: `Đã đổi tên thư mục '${folderName}' thành '${validatedData.newName}'`,
      });
    } catch (error) {
      // Xử lý lỗi xác thực Zod
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: error.errors[0].message,
        });
      }

      // Các lỗi khác
      logError(`Lỗi khi đổi tên thư mục: ${folderName}`, error);

      return res
        .status(error.message.includes("không tồn tại") ? 404 : 500)
        .json({
          success: false,
          error: error.message,
        });
    }
  });

  /**
   * Xóa thư mục
   * @route DELETE /api/folders/:folderName
   */
  public deleteFolder = asyncHandler(async (req: Request, res: Response) => {
    const { folderName } = req.params;

    try {
      // Xác thực dữ liệu đầu vào
      const validatedData = deleteFolderSchema.parse(req.body);

      // Xóa thư mục
      await folderService.deleteFolder(folderName, validatedData.force);

      return res.status(200).json({
        success: true,
        message: `Đã xóa thư mục: ${folderName}`,
      });
    } catch (error) {
      // Xử lý lỗi xác thực Zod
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: error.errors[0].message,
        });
      }

      // Nếu lỗi là do thư mục không rỗng
      if (error.message.includes("không rỗng")) {
        return res.status(409).json({
          success: false,
          error: error.message,
          requireForce: true,
        });
      }

      // Các lỗi khác
      logError(`Lỗi khi xóa thư mục: ${folderName}`, error);

      return res
        .status(error.message.includes("không tồn tại") ? 404 : 500)
        .json({
          success: false,
          error: error.message,
        });
    }
  });

  /**
   * Di chuyển hình ảnh giữa các thư mục
   * @route POST /api/folders/:folderName/move-image
   */
  public moveImage = asyncHandler(async (req: Request, res: Response) => {
    const { folderName } = req.params;

    try {
      // Xác thực dữ liệu đầu vào
      const validatedData = moveImageSchema.parse(req.body);

      // Di chuyển hình ảnh
      const newFilename = await folderService.moveImage(
        validatedData.imageName,
        folderName,
        validatedData.targetFolder
      );

      return res.status(200).json({
        success: true,
        sourceFolder: folderName,
        targetFolder: validatedData.targetFolder,
        originalName: validatedData.imageName,
        newName: newFilename,
        message: `Đã di chuyển hình ảnh từ '${folderName}' sang '${validatedData.targetFolder}'`,
      });
    } catch (error) {
      // Xử lý lỗi xác thực Zod
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          error: error.errors[0].message,
        });
      }

      // Các lỗi khác
      logError(`Lỗi khi di chuyển hình ảnh từ thư mục: ${folderName}`, error);

      const errorMessage = error.message;
      let statusCode = 500;

      // Xác định mã trạng thái dựa trên thông điệp lỗi
      if (errorMessage.includes("không tồn tại trong thư mục")) {
        statusCode = 404;
      } else if (errorMessage.includes("không tồn tại")) {
        statusCode = 404;
      }

      return res.status(statusCode).json({
        success: false,
        error: errorMessage,
      });
    }
  });
}

export default new FolderController();
