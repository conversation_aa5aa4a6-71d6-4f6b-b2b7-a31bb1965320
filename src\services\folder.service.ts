import fs from "fs";
import path from "path";
import { promisify } from "util";
import config from "../config/config";
import { logError, logInfo } from "../utils/logger";
import _ from "lodash";

// Chuyển đổi các hàm callback của fs thành Promise
const mkdir = promisify(fs.mkdir);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const rename = promisify(fs.rename);
const rmdir = promisify(fs.rmdir);

/**
 * Interface cho thông tin thư mục
 */
interface FolderInfo {
  name: string;
  path: string;
  imageCount: number;
  createdAt: Date;
}

/**
 * Lớp dịch vụ quản lý thư mục
 */
class FolderService {
  private readonly basePath: string;

  constructor() {
    this.basePath = config.uploadDir;
    // Đ<PERSON><PERSON> bả<PERSON> thư mục gốc tồn tại
    this.ensureBaseDirectoryExists();
  }

  /**
   * <PERSON><PERSON><PERSON> bảo thư mục gốc tồn tại
   */
  private async ensureBaseDirectoryExists(): Promise<void> {
    try {
      await mkdir(this.basePath, { recursive: true });
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code !== "EEXIST") {
        logError("Lỗi khi tạo thư mục gốc uploads", error);
        throw error;
      }
    }
  }

  /**
   * Tạo thư mục mới
   * @param folderName Tên thư mục
   */
  public async createFolder(folderName: string): Promise<FolderInfo> {
    try {
      // Loại bỏ các ký tự không hợp lệ và chuẩn hóa tên thư mục
      const sanitizedName = folderName.trim().replace(/[^a-zA-Z0-9_-]/g, "_");

      if (!sanitizedName) {
        throw new Error("Tên thư mục không hợp lệ");
      }

      const folderPath = path.join(this.basePath, sanitizedName);

      // Kiểm tra xem thư mục đã tồn tại chưa
      try {
        await stat(folderPath);
        throw new Error(`Thư mục '${sanitizedName}' đã tồn tại`);
      } catch (error) {
        if ((error as NodeJS.ErrnoException).code !== "ENOENT") {
          throw error;
        }
        // Tiếp tục nếu thư mục chưa tồn tại (ENOENT = không tìm thấy)
      }

      // Tạo thư mục
      await mkdir(folderPath, { recursive: true });

      logInfo(`Đã tạo thư mục mới: ${folderPath}`);

      const folderStat = await stat(folderPath);
      return {
        name: sanitizedName,
        path: folderPath,
        imageCount: 0,
        createdAt: folderStat.birthtime,
      };
    } catch (error) {
      logError("Lỗi khi tạo thư mục", error);
      throw error;
    }
  }

  /**
   * Lấy danh sách tất cả các thư mục
   */
  public async getAllFolders(): Promise<FolderInfo[]> {
    try {
      const items = await readdir(this.basePath);
      const folderInfoPromises = items.map(async (item) => {
        const itemPath = path.join(this.basePath, item);
        const itemStat = await stat(itemPath);

        if (itemStat.isDirectory()) {
          // Đếm số lượng hình ảnh trong thư mục
          const files = await this.getImagesInFolder(item);

          return {
            name: item,
            path: itemPath,
            imageCount: files.length,
            createdAt: itemStat.birthtime,
          };
        }
        return null;
      });

      const results = await Promise.all(folderInfoPromises);
      return _.compact(results);
    } catch (error) {
      logError("Lỗi khi lấy danh sách thư mục", error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết về một thư mục cụ thể
   * @param folderName Tên thư mục
   */
  public async getFolderInfo(folderName: string): Promise<FolderInfo> {
    try {
      const folderPath = path.join(this.basePath, folderName);
      const folderStat = await stat(folderPath);

      if (!folderStat.isDirectory()) {
        throw new Error(`'${folderName}' không phải là thư mục`);
      }

      const files = await this.getImagesInFolder(folderName);

      return {
        name: folderName,
        path: folderPath,
        imageCount: files.length,
        createdAt: folderStat.birthtime,
      };
    } catch (error) {
      logError(`Lỗi khi lấy thông tin thư mục: ${folderName}`, error);
      throw error;
    }
  }

  /**
   * Lấy danh sách hình ảnh trong một thư mục
   * @param folderName Tên thư mục
   */
  public async getImagesInFolder(folderName: string): Promise<string[]> {
    try {
      const folderPath = path.join(this.basePath, folderName);
      const files = await readdir(folderPath);

      // Lọc ra chỉ các tệp hình ảnh
      const imageFiles = [];
      for (const file of files) {
        const filePath = path.join(folderPath, file);
        const fileStat = await stat(filePath);
        if (fileStat.isFile()) {
          const ext = path.extname(file).toLowerCase();
          if (config.allowedExtensions.includes(ext)) {
            imageFiles.push(file);
          }
        }
      }

      return imageFiles;
    } catch (error) {
      logError(
        `Lỗi khi lấy danh sách hình ảnh trong thư mục: ${folderName}`,
        error
      );
      throw error;
    }
  }

  /**
   * Đổi tên thư mục
   * @param oldName Tên thư mục cũ
   * @param newName Tên thư mục mới
   */
  public async renameFolder(
    oldName: string,
    newName: string
  ): Promise<FolderInfo> {
    try {
      // Chuẩn hóa tên thư mục mới
      const sanitizedNewName = newName.trim().replace(/[^a-zA-Z0-9_-]/g, "_");

      if (!sanitizedNewName) {
        throw new Error("Tên thư mục mới không hợp lệ");
      }

      const oldPath = path.join(this.basePath, oldName);
      const newPath = path.join(this.basePath, sanitizedNewName);

      // Kiểm tra xem thư mục cũ có tồn tại không
      try {
        await stat(oldPath);
      } catch (error) {
        throw new Error(`Thư mục '${oldName}' không tồn tại`);
      }

      // Kiểm tra xem thư mục mới đã tồn tại chưa
      try {
        await stat(newPath);
        throw new Error(`Thư mục '${sanitizedNewName}' đã tồn tại`);
      } catch (error) {
        if ((error as NodeJS.ErrnoException).code !== "ENOENT") {
          throw error;
        }
        // Tiếp tục nếu thư mục mới chưa tồn tại (ENOENT)
      }

      // Đổi tên thư mục
      await rename(oldPath, newPath);

      logInfo(`Đã đổi tên thư mục '${oldName}' thành '${sanitizedNewName}'`);

      const files = await this.getImagesInFolder(sanitizedNewName);
      const folderStat = await stat(newPath);

      return {
        name: sanitizedNewName,
        path: newPath,
        imageCount: files.length,
        createdAt: folderStat.birthtime,
      };
    } catch (error) {
      logError(
        `Lỗi khi đổi tên thư mục: ${
          error instanceof Error ? error.message : "Lỗi không xác định"
        }`,
        error
      );
      throw error;
    }
  }

  /**
   * Xóa thư mục
   * @param folderName Tên thư mục
   * @param force Nếu true, xóa thư mục ngay cả khi có hình ảnh bên trong
   */
  public async deleteFolder(
    folderName: string,
    force: boolean = false
  ): Promise<boolean> {
    try {
      const folderPath = path.join(this.basePath, folderName);
      const files = await this.getImagesInFolder(folderName);

      if (files.length > 0 && !force) {
        throw new Error(
          `Thư mục '${folderName}' không rỗng (${files.length} hình ảnh). Sử dụng force=true để xóa.`
        );
      }

      // Nếu có tệp và force=true, xóa tất cả tệp trước
      if (files.length > 0 && force) {
        for (const file of files) {
          const filePath = path.join(folderPath, file);
          fs.unlinkSync(filePath); // Xóa đồng bộ để đảm bảo hoàn tất trước khi xóa thư mục
        }
      }

      // Xóa thư mục
      await rmdir(folderPath);

      logInfo(`Đã xóa thư mục: ${folderPath}`);
      return true;
    } catch (error) {
      logError(`Lỗi khi xóa thư mục: ${folderName}`, error);
      throw error;
    }
  }

  /**
   * Di chuyển hình ảnh giữa các thư mục
   * @param imageName Tên tệp hình ảnh
   * @param sourceFolder Thư mục nguồn
   * @param targetFolder Thư mục đích
   */
  public async moveImage(
    imageName: string,
    sourceFolder: string,
    targetFolder: string
  ): Promise<string> {
    try {
      const sourcePath = path.join(this.basePath, sourceFolder, imageName);
      const targetPath = path.join(this.basePath, targetFolder, imageName);

      // Kiểm tra xem tệp nguồn có tồn tại không
      try {
        await stat(sourcePath);
      } catch (error) {
        throw new Error(
          `Hình ảnh '${imageName}' không tồn tại trong thư mục '${sourceFolder}'`
        );
      }

      // Kiểm tra xem thư mục đích có tồn tại không
      try {
        const targetStat = await stat(path.join(this.basePath, targetFolder));
        if (!targetStat.isDirectory()) {
          throw new Error(`'${targetFolder}' không phải là thư mục`);
        }
      } catch (error) {
        if ((error as NodeJS.ErrnoException).code === "ENOENT") {
          throw new Error(`Thư mục đích '${targetFolder}' không tồn tại`);
        }
        throw error;
      }

      // Kiểm tra xem tệp đích đã tồn tại chưa
      try {
        await stat(targetPath);
        // Nếu tệp đã tồn tại, đổi tên tệp đích để tránh ghi đè
        const ext = path.extname(imageName);
        const baseName = path.basename(imageName, ext);
        const newName = `${baseName}_${Date.now()}${ext}`;
        const newTargetPath = path.join(this.basePath, targetFolder, newName);

        await rename(sourcePath, newTargetPath);
        logInfo(
          `Đã di chuyển hình ảnh '${imageName}' từ '${sourceFolder}' đến '${targetFolder}' với tên mới '${newName}'`
        );
        return newName;
      } catch (error) {
        if ((error as NodeJS.ErrnoException).code !== "ENOENT") {
          throw error;
        }

        // Tệp đích chưa tồn tại, di chuyển trực tiếp
        await rename(sourcePath, targetPath);
        logInfo(
          `Đã di chuyển hình ảnh '${imageName}' từ '${sourceFolder}' đến '${targetFolder}'`
        );
        return imageName;
      }
    } catch (error) {
      logError(
        `Lỗi khi di chuyển hình ảnh: ${
          error instanceof Error ? error.message : "Lỗi không xác định"
        }`,
        error
      );
      throw error;
    }
  }
}

export default new FolderService();
