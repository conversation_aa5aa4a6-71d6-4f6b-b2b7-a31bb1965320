import sharp from "sharp";
import path from "path";
import fs from "fs";
import config from "../config/config";
import { logError, logInfo } from "../utils/logger";

/**
 * Interface cho kết quả tối ưu hóa hình ảnh
 */
interface OptimizedImageResult {
  success: boolean;
  outputPath: string;
  error?: string;
  width?: number;
  height?: number;
  size?: number;
  format?: string;
}

/**
 * Lớp dịch vụ tối ưu hóa hình ảnh
 */
class ImageOptimizerService {
  /**
   * Tối ưu hóa hình ảnh dựa trên đường dẫn đã cho
   * @param inputPath Đường dẫn hình ảnh đầu vào
   * @param outputDir Thư mục đầu ra (tùy chọn)
   */
  public async optimizeImage(
    inputPath: string,
    outputDir?: string
  ): Promise<OptimizedImageResult> {
    try {
      // Xác định đường dẫn đầu ra
      const fileName = path.basename(inputPath);
      const outputDirectory = outputDir || path.dirname(inputPath);
      const outputPath = path.join(outputDirectory, `optimized-${fileName}`);

      // Đọc thông tin định dạng của ảnh đầu vào
      const metadata = await sharp(inputPath).metadata();
      const format = metadata.format;

      // Tạo một instance Sharp để xử lý ảnh
      let sharpInstance = sharp(inputPath);

      // Thay đổi kích thước hình ảnh
      sharpInstance = sharpInstance.resize({
        width: config.imageWidth,
        height: config.imageHeight,
        fit: "cover", // Đảm bảo ảnh đủ kích thước và cắt phần thừa
        position: "centre", // Căn giữa khi cắt
      });

      // Cấu hình định dạng đầu ra dựa trên định dạng gốc
      if (format === "jpeg" || format === "jpg") {
        sharpInstance = sharpInstance.jpeg({ quality: config.imageQuality });
      } else if (format === "png") {
        sharpInstance = sharpInstance.png({ quality: config.imageQuality });
      }

      // Lưu hình ảnh đã tối ưu
      await sharpInstance.toFile(outputPath);

      // Lấy thông tin về hình ảnh đã tối ưu
      const outputInfo = await sharp(outputPath).metadata();
      const stats = fs.statSync(outputPath);

      logInfo(`Đã tối ưu hóa hình ảnh thành công: ${outputPath}`);

      return {
        success: true,
        outputPath,
        width: outputInfo.width,
        height: outputInfo.height,
        format: outputInfo.format,
        size: stats.size,
      };
    } catch (error) {
      logError("Lỗi khi tối ưu hóa hình ảnh", error);
      return {
        success: false,
        outputPath: "",
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      };
    }
  }

  /**
   * Tạo hình ảnh thu nhỏ từ ảnh đầu vào
   * @param inputPath Đường dẫn hình ảnh đầu vào
   */
  public async createThumbnail(
    inputPath: string
  ): Promise<OptimizedImageResult> {
    try {
      const fileName = path.basename(inputPath);
      const outputDir = path.dirname(inputPath);
      const outputPath = path.join(outputDir, `thumbnail-${fileName}`);

      // Tạo hình ảnh thu nhỏ với kích thước cố định
      await sharp(inputPath)
        .resize({
          width: 200,
          height: 200,
          fit: "cover",
          position: "centre",
        })
        .toFile(outputPath);

      const info = await sharp(outputPath).metadata();

      return {
        success: true,
        outputPath,
        width: info.width,
        height: info.height,
        format: info.format,
      };
    } catch (error) {
      logError("Lỗi khi tạo hình ảnh thu nhỏ", error);
      return {
        success: false,
        outputPath: "",
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      };
    }
  }
}

export default new ImageOptimizerService();
