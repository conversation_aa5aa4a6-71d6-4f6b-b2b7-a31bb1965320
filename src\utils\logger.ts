import winston from "winston";
import path from "path";

// <PERSON><PERSON><PERSON> nghĩa c<PERSON>u hình logger
const logger = winston.createLogger({
  level: process.env.NODE_ENV === "production" ? "info" : "debug",
  format: winston.format.combine(
    winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json()
  ),
  defaultMeta: { service: "image-server" },
  transports: [
    // <PERSON><PERSON> tất cả logs từ mức error trở lên vào file error.log
    new winston.transports.File({
      filename: path.join("logs", "error.log"),
      level: "error",
    }),
    // <PERSON><PERSON> tất cả logs vào file combined.log
    new winston.transports.File({
      filename: path.join("logs", "combined.log"),
    }),
  ],
});

// Thêm console transport khi không ở chế độ production
if (process.env.NODE_ENV !== "production") {
  logger.add(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    })
  );
}

// Tạo các hàm tiện ích ghi log
export const logInfo = (message: string, meta?: any): void => {
  logger.info(message, meta);
};

export const logError = (message: string, error?: Error | unknown): void => {
  if (error instanceof Error) {
    logger.error(`${message}: ${error.message}`, { stack: error.stack });
  } else {
    logger.error(message, { error });
  }
};

export const logWarning = (message: string, meta?: any): void => {
  logger.warn(message, meta);
};

export const logDebug = (message: string, meta?: any): void => {
  logger.debug(message, meta);
};

export default logger;
