import { Router } from "express";
import folderController from "../controllers/folder.controller";

const router = Router();

// T<PERSON>o thư mục mới
router.post("/", folderController.createFolder);

// L<PERSON><PERSON> tất cả thư mục
router.get("/", folderController.getAllFolders);

// L<PERSON>y thông tin chi tiết về một thư mục
router.get("/:folderName", folderController.getFolderInfo);

// L<PERSON>y danh sách hình ảnh trong một thư mục
router.get("/:folderName/images", folderController.getImagesInFolder);

// Đ<PERSON>i tên thư mục
router.put("/:folderName/rename", folderController.renameFolder);

// X<PERSON>a thư mục
router.delete("/:folderName", folderController.deleteFolder);

// <PERSON> hình ảnh gi<PERSON>a các thư mục
router.post("/:folderName/move-image", folderController.moveImage);

export default router;
