<!DOCTYPE html>
<html lang="vi" class="h-full">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      <%= typeof title !== 'undefined' ? title : 'Image Management Server' %>
    </title>

    <!-- Tailwind CSS via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: "class",
        theme: {
          extend: {
            colors: {
              primary: {
                50: "#f0f9ff",
                100: "#e0f2fe",
                200: "#bae6fd",
                300: "#7dd3fc",
                400: "#38bdf8",
                500: "#0ea5e9",
                600: "#0284c7",
                700: "#0369a1",
                800: "#075985",
                900: "#0c4a6e",
              },
            },
          },
        },
      };
    </script>

    <!-- Font <PERSON>wesome cho biểu tượng -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />

    <!-- Tùy chỉnh CSS -->
    <style>
      .image-card {
        transition: all 0.2s ease;
      }
      .image-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }
      .folder-card {
        transition: all 0.2s ease;
      }
      .folder-card:hover {
        transform: translateY(-2px);
      }
      .loading {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
      }
      .dark .loading {
        background-color: rgba(17, 24, 39, 0.7);
      }
    </style>
  </head>
  <body class="h-full bg-gray-50 dark:bg-gray-900">
    <!-- Thanh điều hướng -->
    <nav class="bg-white dark:bg-gray-800 shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex">
            <div class="flex-shrink-0 flex items-center">
              <a
                href="/"
                class="text-xl font-bold text-primary-600 dark:text-primary-400"
              >
                <i class="fas fa-images mr-2"></i>
                Image Manager
              </a>
            </div>
            <div class="ml-6 flex space-x-8">
              <a
                href="/"
                class="inline-flex items-center px-1 pt-1 border-b-2 border-primary-500 dark:border-primary-400 text-sm font-medium text-gray-900 dark:text-gray-100"
              >
                Trang chủ
              </a>
              <a
                href="/gallery"
                class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600"
              >
                Thư viện
              </a>
              <a
                href="/upload"
                class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600"
              >
                Tải lên
              </a>
              <a
                href="/folders"
                class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600"
              >
                Thư mục
              </a>
              <a
                href="/backups"
                class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-200 hover:border-gray-300 dark:hover:border-gray-600"
              >
                Sao lưu
              </a>
            </div>
          </div>
          <div class="flex items-center">
            <button
              id="theme-toggle"
              type="button"
              class="p-2 text-gray-500 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
            >
              <i id="theme-toggle-dark-icon" class="hidden fas fa-moon"></i>
              <i id="theme-toggle-light-icon" class="hidden fas fa-sun"></i>
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Container chính -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Tiêu đề trang -->
      <% if (typeof pageTitle !== 'undefined') { %>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
        <%= pageTitle %>
      </h1>
      <% } %>

      <!-- Nội dung trang -->
      <%- typeof content !== 'undefined' ? content : '' %>
    </main>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 shadow mt-auto">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <p class="text-center text-gray-500 dark:text-gray-400">
          &copy; <%= new Date().getFullYear() %> Image Management Server
        </p>
      </div>
    </footer>

    <!-- Lớp phủ hiệu ứng loading -->
    <div id="loading" class="loading hidden">
      <div class="text-center">
        <div
          class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 dark:border-primary-400"
        ></div>
        <p class="mt-3 text-gray-700 dark:text-gray-300">Đang xử lý...</p>
      </div>
    </div>

    <!-- Script chuyển đổi chủ đề -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Lấy các nút chuyển đổi chủ đề và biểu tượng
        const themeToggle = document.getElementById("theme-toggle");
        const darkIcon = document.getElementById("theme-toggle-dark-icon");
        const lightIcon = document.getElementById("theme-toggle-light-icon");

        // Kiểm tra chủ đề từ localStorage
        if (
          localStorage.getItem("color-theme") === "dark" ||
          (!("color-theme" in localStorage) &&
            window.matchMedia("(prefers-color-scheme: dark)").matches)
        ) {
          document.documentElement.classList.add("dark");
          lightIcon.classList.remove("hidden");
        } else {
          darkIcon.classList.remove("hidden");
        }

        // Xử lý sự kiện click vào nút chuyển đổi chủ đề
        themeToggle.addEventListener("click", function () {
          darkIcon.classList.toggle("hidden");
          lightIcon.classList.toggle("hidden");

          // Nếu đang ở chế độ sáng
          if (document.documentElement.classList.contains("dark")) {
            document.documentElement.classList.remove("dark");
            localStorage.setItem("color-theme", "light");
          } else {
            document.documentElement.classList.add("dark");
            localStorage.setItem("color-theme", "dark");
          }
        });
      });

      // Hàm hiển thị loading
      function showLoading() {
        document.getElementById("loading").classList.remove("hidden");
      }

      // Hàm ẩn loading
      function hideLoading() {
        document.getElementById("loading").classList.add("hidden");
      }

      // Sao chép văn bản vào clipboard
      function copyToClipboard(text) {
        navigator.clipboard
          .writeText(text)
          .then(() => {
            // Hiển thị thông báo thành công
            showNotification("Đã sao chép vào clipboard!", "success");
          })
          .catch((err) => {
            // Hiển thị thông báo lỗi
            showNotification("Không thể sao chép: " + err, "error");
          });
      }

      // Hiển thị thông báo
      function showNotification(message, type = "info") {
        const notification = document.createElement("div");
        notification.className = `fixed bottom-4 right-4 px-4 py-2 rounded-md shadow-lg transition-all duration-300 transform translate-y-0 opacity-100 z-50`;

        // Màu sắc dựa trên loại thông báo
        switch (type) {
          case "success":
            notification.classList.add("bg-green-500", "text-white");
            break;
          case "error":
            notification.classList.add("bg-red-500", "text-white");
            break;
          case "warning":
            notification.classList.add("bg-yellow-500", "text-white");
            break;
          default:
            notification.classList.add("bg-blue-500", "text-white");
        }

        notification.textContent = message;
        document.body.appendChild(notification);

        // Xóa thông báo sau 3 giây
        setTimeout(() => {
          notification.classList.replace("translate-y-0", "translate-y-10");
          notification.classList.replace("opacity-100", "opacity-0");
          setTimeout(() => {
            document.body.removeChild(notification);
          }, 300);
        }, 3000);
      }
    </script>

    <!-- Script cho trang cụ thể -->
    <% if (typeof pageScript !== 'undefined') { %> <%- pageScript %> <% } %>
  </body>
</html>
