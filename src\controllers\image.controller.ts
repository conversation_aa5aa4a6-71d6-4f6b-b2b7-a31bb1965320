import { Request, Response, NextFunction } from "express";
import path from "path";
import imageService from "../services/image.service";
import folderService from "../services/folder.service";
import { asyncHandler } from "../middleware/error-handler.middleware";
import { logInfo, logError } from "../utils/logger";
import config from "../config/config";

/**
 * Controller xử lý các tác vụ liên quan đến hình ảnh
 */
class ImageController {
  /**
   * Tải lên hình ảnh mới
   * @route POST /api/images/upload
   */
  public uploadImage = asyncHandler(async (req: Request, res: Response) => {
    // Kiểm tra tệp đã được tải lên bởi multer chưa
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: "Không có tệp hình ảnh nào được tải lên",
      });
    }

    // <PERSON><PERSON><PERSON> thư mục từ yêu cầu hoặc sử dụng giá trị mặc định
    const folder = req.body.folder || "default";

    try {
      // Tạo thư mục nếu chưa tồn tại
      try {
        await folderService.getFolderInfo(folder);
      } catch (error) {
        await folderService.createFolder(folder);
      }

      // Tạo hình ảnh
      const image = await imageService.createImage(req.file, folder);

      // Trả về thông tin hình ảnh đã tạo
      return res.status(201).json({
        success: true,
        data: image,
        message: "Tải lên hình ảnh thành công",
      });
    } catch (error) {
      logError("Lỗi khi tải lên hình ảnh", error);

      // Nếu đã tạo tệp nhưng xử lý thất bại, cần xóa tệp
      if (req.file && req.file.path) {
        try {
          const fs = require("fs");
          fs.unlinkSync(req.file.path);
        } catch (unlinkError) {
          logError("Không thể xóa tệp tạm", unlinkError);
        }
      }

      throw error;
    }
  });

  /**
   * Lấy tất cả hình ảnh
   * @route GET /api/images
   */
  public getAllImages = asyncHandler(async (_req: Request, res: Response) => {
    const images = imageService.getAllImages();

    return res.status(200).json({
      success: true,
      count: images.length,
      data: images,
    });
  });

  /**
   * Lấy hình ảnh theo ID
   * @route GET /api/images/:id
   */
  public getImageById = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const image = imageService.getImageById(id);

    if (!image) {
      return res.status(404).json({
        success: false,
        error: `Không tìm thấy hình ảnh với ID: ${id}`,
      });
    }

    return res.status(200).json({
      success: true,
      data: image,
    });
  });

  /**
   * Lấy hình ảnh theo thư mục
   * @route GET /api/images/folder/:folderName
   */
  public getImagesByFolder = asyncHandler(
    async (req: Request, res: Response) => {
      const { folderName } = req.params;
      const images = imageService.getImagesByFolder(folderName);

      return res.status(200).json({
        success: true,
        folder: folderName,
        count: images.length,
        data: images,
      });
    }
  );

  /**
   * Đổi tên hình ảnh
   * @route PUT /api/images/:id/rename
   */
  public renameImage = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { newFilename } = req.body;

    if (!newFilename) {
      return res.status(400).json({
        success: false,
        error: "Tên tệp mới không được cung cấp",
      });
    }

    const image = await imageService.renameImage(id, newFilename);

    if (!image) {
      return res.status(404).json({
        success: false,
        error: `Không tìm thấy hình ảnh với ID: ${id}`,
      });
    }

    return res.status(200).json({
      success: true,
      data: image,
      message: "Đổi tên hình ảnh thành công",
    });
  });

  /**
   * Di chuyển hình ảnh sang thư mục khác
   * @route PUT /api/images/:id/move
   */
  public moveImage = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const { targetFolder } = req.body;

    if (!targetFolder) {
      return res.status(400).json({
        success: false,
        error: "Thư mục đích không được cung cấp",
      });
    }

    // Kiểm tra xem thư mục đích có tồn tại không
    try {
      await folderService.getFolderInfo(targetFolder);
    } catch (error) {
      // Nếu không, tạo thư mục mới
      await folderService.createFolder(targetFolder);
    }

    const image = await imageService.moveImage(id, targetFolder);

    if (!image) {
      return res.status(404).json({
        success: false,
        error: `Không tìm thấy hình ảnh với ID: ${id}`,
      });
    }

    return res.status(200).json({
      success: true,
      data: image,
      message: `Đã di chuyển hình ảnh sang thư mục: ${targetFolder}`,
    });
  });

  /**
   * Xóa hình ảnh
   * @route DELETE /api/images/:id
   */
  public deleteImage = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const success = await imageService.deleteImage(id);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: `Không tìm thấy hình ảnh với ID: ${id}`,
      });
    }

    return res.status(200).json({
      success: true,
      message: "Đã xóa hình ảnh thành công",
    });
  });

  /**
   * Quét và đồng bộ hóa thư mục tải lên
   * @route POST /api/images/scan
   */
  public scanUploadDirectory = asyncHandler(
    async (_req: Request, res: Response) => {
      await imageService.scanUploadDirectory();

      return res.status(200).json({
        success: true,
        message: "Đã quét và đồng bộ hóa thư mục tải lên thành công",
      });
    }
  );

  /**
   * Lấy URL hình ảnh để sao chép vào clipboard
   * @route GET /api/images/:id/url
   */
  public getImageUrl = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const image = imageService.getImageById(id);

    if (!image) {
      return res.status(404).json({
        success: false,
        error: `Không tìm thấy hình ảnh với ID: ${id}`,
      });
    }

    // Tạo URL đầy đủ bằng cách sử dụng host từ request
    const baseUrl = `${req.protocol}://${req.get("host")}`;
    const imageUrl =
      image.optimized && image.optimizedPath
        ? `${baseUrl}/uploads/${image.folder}/optimized-${image.filename}`
        : `${baseUrl}/uploads/${image.folder}/${image.filename}`;

    return res.status(200).json({
      success: true,
      url: imageUrl,
      thumbnailUrl: image.thumbnailPath
        ? `${baseUrl}/uploads/${image.folder}/thumbnail-${image.filename}`
        : null,
      message: "URL hình ảnh đã sẵn sàng để sao chép",
    });
  });
}

export default new ImageController();
