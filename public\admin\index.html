<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Album Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto p-6">
      <h1 class="text-2xl font-bold mb-4">Quản lý ảnh theo Album</h1>

      <!-- Album Management -->
      <div class="bg-white rounded-xl shadow p-4 mb-6">
        <h2 class="font-semibold mb-3">Quản lý Album</h2>
        <div class="flex gap-2 mb-4">
          <input
            id="newAlbumName"
            class="border rounded-lg p-2 flex-1"
            pattern="[A-Za-z0-9_-]{1,64}"
            placeholder="Nhập tên album mới"
          />
          <button
            id="createAlbum"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg"
          >
            Tạo Album
          </button>
        </div>

        <div class="mb-3">
          <label class="block text-sm font-medium mb-1">Chọn album</label>
          <select id="albumSelector" class="w-full border rounded-lg p-2">
            <option value="">-- Chọn album --</option>
          </select>
        </div>

        <div class="flex gap-2 justify-end">
          <button
            id="deleteAlbum"
            class="px-4 py-2 bg-red-600 text-white rounded-lg hidden"
          >
            Xóa album
          </button>
        </div>
      </div>

      <!-- Upload Form -->
      <div class="bg-white rounded-xl shadow p-4 mb-6">
        <h2 class="font-semibold mb-3">Tải ảnh lên</h2>
        <form id="uploadForm" class="space-y-3">
          <div>
            <label class="block text-sm font-medium">Album</label>
            <input
              id="album"
              required
              pattern="[A-Za-z0-9_-]{1,64}"
              class="mt-1 w-full border rounded-lg p-2"
              placeholder="vd: summer_2024"
              readonly
            />
          </div>
          <div>
            <label class="block text-sm font-medium">Ảnh</label>
            <input
              id="image"
              type="file"
              accept="image/png,image/jpeg,image/webp"
              required
              class="mt-1"
            />
          </div>
          <button
            id="uploadButton"
            class="px-4 py-2 bg-black text-white rounded-lg"
            disabled
          >
            Tải lên
          </button>
          <p id="msg" class="text-sm text-gray-600"></p>
        </form>
      </div>

      <!-- Image Gallery -->
      <div class="bg-white rounded-xl shadow p-4">
        <div class="flex items-center justify-between mb-3">
          <h2 class="font-semibold">Ảnh trong album</h2>
          <button id="refresh" class="text-sm underline">Làm mới</button>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-3 gap-4" id="grid"></div>
      </div>
    </div>

    <script>
      const msg = document.getElementById("msg");
      const grid = document.getElementById("grid");
      const albumInput = document.getElementById("album");
      const albumSelector = document.getElementById("albumSelector");
      const uploadButton = document.getElementById("uploadButton");
      const deleteAlbumButton = document.getElementById("deleteAlbum");

      // Load albums
      async function loadAlbums() {
        try {
          const res = await fetch("/api/albums");
          const data = await res.json();

          albumSelector.innerHTML =
            '<option value="">-- Chọn album --</option>';

          (data.albums || []).forEach((album) => {
            const option = document.createElement("option");
            option.value = album;
            option.textContent = album;
            albumSelector.appendChild(option);
          });

          return data.albums || [];
        } catch (err) {
          console.error("Failed to load albums:", err);
          return [];
        }
      }

      // Create new album
      document
        .getElementById("createAlbum")
        .addEventListener("click", async () => {
          const name = document.getElementById("newAlbumName").value.trim();
          if (!name) return;

          try {
            const res = await fetch("/api/albums", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ name }),
            });

            const data = await res.json();

            if (res.ok) {
              msg.textContent = `Album "${name}" được tạo thành công`;
              document.getElementById("newAlbumName").value = "";
              await loadAlbums();
              albumSelector.value = name;
              albumInput.value = name;
              uploadButton.disabled = false;
              deleteAlbumButton.classList.remove("hidden");
              listImages();
            } else {
              msg.textContent = `Lỗi: ${data.error || "Không thể tạo album"}`;
            }
          } catch (err) {
            msg.textContent = `Lỗi: ${err.message}`;
            console.error("Failed to create album:", err);
          }
        });

      // Delete album
      deleteAlbumButton.addEventListener("click", async () => {
        const album = albumSelector.value;
        if (!album) return;

        if (
          !confirm(
            `Bạn có chắc muốn xóa album "${album}" không? Tất cả ảnh trong album sẽ bị xóa.`
          )
        ) {
          return;
        }

        try {
          const res = await fetch(`/api/albums/${album}`, {
            method: "DELETE",
          });

          if (res.ok) {
            msg.textContent = `Album "${album}" đã được xóa`;
            albumSelector.value = "";
            albumInput.value = "";
            uploadButton.disabled = true;
            deleteAlbumButton.classList.add("hidden");
            grid.innerHTML = "";
            await loadAlbums();
          } else {
            const data = await res.json();
            msg.textContent = `Lỗi: ${data.error || "Không thể xóa album"}`;
          }
        } catch (err) {
          msg.textContent = `Lỗi: ${err.message}`;
          console.error("Failed to delete album:", err);
        }
      });

      // Select album
      albumSelector.addEventListener("change", () => {
        const selected = albumSelector.value;
        albumInput.value = selected;
        uploadButton.disabled = !selected;

        if (selected) {
          deleteAlbumButton.classList.remove("hidden");
          listImages();
        } else {
          deleteAlbumButton.classList.add("hidden");
          grid.innerHTML = "";
        }
      });

      async function listImages() {
        const album = albumInput.value.trim();
        if (!album) return;

        try {
          const res = await fetch(`/api/albums/${album}`);
          const data = await res.json();
          grid.innerHTML = "";

          if (!data.images || data.images.length === 0) {
            grid.innerHTML =
              '<div class="col-span-full text-center py-10 text-gray-500">Chưa có ảnh nào trong album này</div>';
            return;
          }

          data.images.forEach(({ image, thumb }) => {
            // Extract ID from URL
            const urlParts = image.split("/");
            const filename = urlParts[urlParts.length - 1];
            const id = filename.split(".")[0];

            const item = document.createElement("div");
            item.className = "border rounded-lg overflow-hidden bg-gray-100";
            item.innerHTML = `
              <a href="${image}" target="_blank" class="block">
                <img src="${thumb}" alt="thumbnail" class="w-full aspect-square object-cover" />
              </a>
              <div class="p-2 flex items-center justify-between">
                <a href="${image}" target="_blank" class="text-xs text-blue-600 underline">Xem</a>
                <button class="delete-image text-xs bg-red-100 hover:bg-red-200 text-red-600 px-2 py-1 rounded" data-id="${id}">Xóa</button>
              </div>`;
            grid.appendChild(item);
          });

          // Add delete event listeners
          document.querySelectorAll(".delete-image").forEach((btn) => {
            btn.addEventListener("click", deleteImage);
          });
        } catch (err) {
          console.error("Failed to load images:", err);
          grid.innerHTML =
            '<div class="col-span-full text-center py-10 text-red-500">Lỗi khi tải ảnh</div>';
        }
      }

      // Delete image
      async function deleteImage(e) {
        const id = e.target.dataset.id;
        const album = albumInput.value.trim();
        if (!id || !album) return;

        if (!confirm("Bạn có chắc muốn xóa ảnh này không?")) {
          return;
        }

        try {
          const res = await fetch(`/api/albums/${album}/${id}`, {
            method: "DELETE",
          });

          if (res.ok) {
            msg.textContent = "Ảnh đã được xóa";
            listImages(); // Refresh the image list
          } else {
            const data = await res.json();
            msg.textContent = `Lỗi: ${data.error || "Không thể xóa ảnh"}`;
          }
        } catch (err) {
          msg.textContent = `Lỗi: ${err.message}`;
          console.error("Failed to delete image:", err);
        }
      }

      document.getElementById("refresh").addEventListener("click", listImages);

      document
        .getElementById("uploadForm")
        .addEventListener("submit", async (e) => {
          e.preventDefault();
          msg.textContent = "Đang tải lên...";
          const album = albumInput.value.trim();
          const file = document.getElementById("image").files[0];
          const fd = new FormData();
          fd.append("image", file);

          try {
            const res = await fetch(`/api/albums/${album}/images`, {
              method: "POST",
              body: fd,
            });

            const data = await res.json();

            if (res.ok) {
              msg.textContent = `Ảnh đã được tải lên thành công`;
              document.getElementById("image").value = "";
              await listImages();
            } else {
              msg.textContent = `Lỗi: ${data.error || "Không thể tải ảnh lên"}`;
            }
          } catch (err) {
            msg.textContent = `Lỗi: ${err.message}`;
            console.error("Failed to upload image:", err);
          }
        });

      // Initial load
      (async function init() {
        await loadAlbums();
      })();
    </script>
  </body>
</html>
