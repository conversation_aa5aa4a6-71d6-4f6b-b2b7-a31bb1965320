const ALBUM_SAFE = /^[a-zA-Z0-9_-]{1,64}$/; // letters, numbers, _ - , up to 64
const ID_SAFE = /^[a-zA-Z0-9_-]{6,32}$/; // letters, numbers, _ - , 6-32 chars

/**
 * Sanitizes and validates album name
 * @param {string} name - The album name to validate
 * @returns {string|null} - The sanitized album name or null if invalid
 */
export function sanitizeAlbum(name) {
  if (!name || typeof name !== "string") return null;
  name = name.trim();
  if (!ALBUM_SAFE.test(name)) return null;
  return name;
}

/**
 * Validates image ID
 * @param {string} id - The image ID to validate
 * @returns {boolean} - True if valid, false otherwise
 */
export function validateId(id) {
  if (!id || typeof id !== "string") return false;
  return ID_SAFE.test(id.trim());
}

/**
 * Set of allowed MIME types for image uploads
 */
export const allowedMime = new Set([
  "image/jpeg",
  "image/png",
  "image/webp",
  "image/gif",
  "image/avif",
]);

/**
 * Validates output format
 * @param {string} format - The format to validate
 * @returns {boolean} - True if valid, false otherwise
 */
export function validateFormat(format) {
  if (!format || typeof format !== "string") return false;
  format = format.toLowerCase().trim();
  return format === "webp" || format === "avif";
}

/**
 * Validates numeric parameter within range
 * @param {number|string} value - Value to validate
 * @param {number} min - Minimum allowed value
 * @param {number} max - Maximum allowed value
 * @param {number} defaultVal - Default value if validation fails
 * @returns {number} - Validated number or default value
 */
export function validateNumber(value, min, max, defaultVal) {
  const num = Number(value);
  if (isNaN(num)) return defaultVal;
  return Math.min(max, Math.max(min, num));
}
