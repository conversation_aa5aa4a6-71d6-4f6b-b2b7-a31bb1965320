import multer from "multer";
import path from "path";
import fs from "fs";
import { Request } from "express";
import config from "../config/config";
import { logError } from "../utils/logger";

// <PERSON><PERSON><PERSON> bảo thư mục uploads tồn tại
const ensureUploadDirExists = (): void => {
  try {
    if (!fs.existsSync(config.uploadDir)) {
      fs.mkdirSync(config.uploadDir, { recursive: true });
    }
  } catch (error) {
    logError("Không thể tạo thư mục uploads", error);
    throw new Error("Không thể tạo thư mục uploads");
  }
};

ensureUploadDirExists();

// Cấu hình lưu trữ cho multer
const storage = multer.diskStorage({
  destination: (_req: Request, _file: Express.Multer.File, cb) => {
    cb(null, config.uploadDir);
  },
  filename: (_req: Request, file: Express.Multer.File, cb) => {
    // Tạo tên tệp duy nhất với timestamp
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  },
});

// Hàm lọc tệp để chỉ chấp nhận hình ảnh
const fileFilter = (
  _req: Request,
  file: Express.Multer.File,
  cb: multer.FileFilterCallback
) => {
  // Kiểm tra MIME type
  if (!config.allowedMimeTypes.includes(file.mimetype)) {
    return cb(
      new Error("Loại tệp không được hỗ trợ. Chỉ chấp nhận JPG và PNG.")
    );
  }

  // Kiểm tra phần mở rộng tệp
  const extension = path.extname(file.originalname).toLowerCase();
  if (!config.allowedExtensions.includes(extension)) {
    return cb(
      new Error(
        "Phần mở rộng tệp không được hỗ trợ. Chỉ chấp nhận .jpg, .jpeg và .png."
      )
    );
  }

  cb(null, true);
};

// Khởi tạo middleware multer
const upload = multer({
  storage,
  limits: { fileSize: config.maxFileSize },
  fileFilter,
});

export default upload;
