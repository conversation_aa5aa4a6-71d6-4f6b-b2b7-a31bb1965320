import express, { Request, Response, NextFunction } from "express";
import path from "path";
import dotenv from "dotenv";
import rateLimit from "express-rate-limit";
import routes from "./routes";
import config from "./config/config";
import {
  errorHandler,
  notFoundHandler,
} from "./middleware/error-handler.middleware";
import { logInfo } from "./utils/logger";

// Tải biến môi trường
dotenv.config();

// Khởi tạo Express
const app = express();

// Middleware cơ bản
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Cấu hình EJS
app.set("view engine", "ejs");
app.set("views", path.join(__dirname, "views"));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 phút
  max: 100, // Giới hạn mỗi IP 100 yêu cầu trong mỗi windowMs
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    error: "<PERSON>u<PERSON> nhiều yêu cầu từ IP này, vui lòng thử lại sau 15 phút.",
  },
});

// Áp dụng rate limiting cho tất cả yêu cầu API
app.use("/api", limiter);

// Phục vụ tệp tĩnh từ thư mục public
app.use(
  express.static(path.join(__dirname, "public"), {
    setHeaders: (res, filePath) => {
      // Thêm headers cho tệp hình ảnh
      if (
        filePath.endsWith(".jpg") ||
        filePath.endsWith(".jpeg") ||
        filePath.endsWith(".png")
      ) {
        res.setHeader("Cache-Control", "public, max-age=86400"); // Cache 1 ngày
        res.setHeader("Content-Disposition", "inline");
      }
    },
  })
);

// CORS middleware
app.use((_req: Request, res: Response, next: NextFunction) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE");
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization"
  );
  next();
});

// Logger middleware
app.use((req: Request, _res: Response, next: NextFunction) => {
  logInfo(`${req.method} ${req.url}`, { ip: req.ip });
  next();
});

// API routes
app.use(routes);

// Trang chủ
app.get("/", (_req: Request, res: Response) => {
  res.render("index", {
    title: "Image Management Server",
    baseUrl: `${_req.protocol}://${_req.get("host")}`,
  });
});

// Xử lý đường dẫn không tồn tại
app.use("*", notFoundHandler);

// Xử lý lỗi
app.use(errorHandler);

export default app;
