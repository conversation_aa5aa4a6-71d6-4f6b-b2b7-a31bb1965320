import fs from "fs";
import path from "path";
import { promisify } from "util";
import Image, { ImageModel } from "../models/image.model";
import config from "../config/config";
import { logError, logInfo } from "../utils/logger";
import imageOptimizerService from "./image-optimizer.service";

// Chuyển đổi các hàm callback thành Promise
const unlink = promisify(fs.unlink);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

/**
 * Lớp dịch vụ quản lý hình ảnh
 */
class ImageService {
  // Lưu trữ dữ liệu hình ảnh trong bộ nhớ (trong thực tế nên sử dụng cơ sở dữ liệu)
  private images: Map<string, Image> = new Map<string, Image>();

  /**
   * Tạo hình ảnh mới từ tệp đã tải lên
   * @param file Tệp từ middleware multer
   * @param folder Th<PERSON> mục lưu trữ
   */
  public async createImage(
    file: Express.Multer.File,
    folder: string = "default"
  ): Promise<Image> {
    try {
      // Lấy thông tin tệp
      const extension = path.extname(file.originalname).toLowerCase();
      const filePath = file.path;

      // Tạo đối tượng hình ảnh
      const image = new Image({
        filename: file.filename,
        originalname: file.originalname,
        mimetype: file.mimetype,
        path: filePath,
        size: file.size,
        extension: extension,
        folder: folder,
      });

      // Tối ưu hóa hình ảnh nếu cấu hình cho phép
      try {
        const optimizationResult = await imageOptimizerService.optimizeImage(
          filePath
        );
        if (optimizationResult.success) {
          image.optimized = true;
          image.optimizedPath = optimizationResult.outputPath;
          image.width = optimizationResult.width;
          image.height = optimizationResult.height;

          // Tạo hình ảnh thu nhỏ
          const thumbnailResult = await imageOptimizerService.createThumbnail(
            filePath
          );
          if (thumbnailResult.success) {
            image.thumbnailPath = thumbnailResult.outputPath;
          }
        }
      } catch (error) {
        logError("Lỗi khi tối ưu hóa hình ảnh", error);
        // Tiếp tục xử lý hình ảnh gốc nếu tối ưu hóa thất bại
      }

      // Tạo URL truy cập
      const relativePathInPublic = path.relative(
        path.join(process.cwd(), "src/public"),
        filePath
      );
      image.url = `/uploads/${folder}/${path.basename(filePath)}`;

      // Lưu hình ảnh vào bộ nhớ
      this.images.set(image.id, image);

      logInfo(`Đã tạo hình ảnh mới: ${image.id}`);

      return image;
    } catch (error) {
      logError("Lỗi khi tạo hình ảnh", error);
      throw error;
    }
  }

  /**
   * Lấy thông tin hình ảnh theo ID
   * @param id ID hình ảnh
   */
  public getImageById(id: string): Image | undefined {
    return this.images.get(id);
  }

  /**
   * Lấy tất cả hình ảnh
   */
  public getAllImages(): Image[] {
    return Array.from(this.images.values());
  }

  /**
   * Lấy hình ảnh theo thư mục
   * @param folder Tên thư mục
   */
  public getImagesByFolder(folder: string): Image[] {
    return Array.from(this.images.values()).filter(
      (image) => image.folder === folder
    );
  }

  /**
   * Đổi tên tệp hình ảnh
   * @param id ID hình ảnh
   * @param newFilename Tên tệp mới
   */
  public async renameImage(
    id: string,
    newFilename: string
  ): Promise<Image | null> {
    try {
      const image = this.images.get(id);

      if (!image) {
        return null;
      }

      // Lưu đường dẫn cũ
      const oldPath = image.path;
      const directory = path.dirname(oldPath);
      const extension = path.extname(oldPath);

      // Tạo đường dẫn mới
      const sanitizedName = newFilename.trim().replace(/[^a-zA-Z0-9_-]/g, "_");
      const newPath = path.join(directory, `${sanitizedName}${extension}`);

      // Đổi tên tệp
      fs.renameSync(oldPath, newPath);

      // Cập nhật thông tin hình ảnh
      image.filename = `${sanitizedName}${extension}`;
      image.path = newPath;
      image.url = `/uploads/${image.folder}/${sanitizedName}${extension}`;

      // Cập nhật đường dẫn tối ưu và hình ảnh thu nhỏ nếu có
      if (image.optimized && image.optimizedPath) {
        const oldOptPath = image.optimizedPath;
        const oldOptDir = path.dirname(oldOptPath);
        const newOptPath = path.join(
          oldOptDir,
          `optimized-${sanitizedName}${extension}`
        );
        fs.renameSync(oldOptPath, newOptPath);
        image.optimizedPath = newOptPath;
      }

      if (image.thumbnailPath) {
        const oldThumbPath = image.thumbnailPath;
        const oldThumbDir = path.dirname(oldThumbPath);
        const newThumbPath = path.join(
          oldThumbDir,
          `thumbnail-${sanitizedName}${extension}`
        );
        fs.renameSync(oldThumbPath, newThumbPath);
        image.thumbnailPath = newThumbPath;
      }

      logInfo(`Đã đổi tên hình ảnh ${id}: ${oldPath} -> ${newPath}`);

      return image;
    } catch (error) {
      logError(`Lỗi khi đổi tên hình ảnh ${id}`, error);
      throw error;
    }
  }

  /**
   * Di chuyển hình ảnh sang thư mục khác
   * @param id ID hình ảnh
   * @param targetFolder Thư mục đích
   */
  public async moveImage(
    id: string,
    targetFolder: string
  ): Promise<Image | null> {
    try {
      const image = this.images.get(id);

      if (!image) {
        return null;
      }

      // Lưu đường dẫn cũ
      const oldPath = image.path;
      const filename = path.basename(oldPath);

      // Tạo đường dẫn mới
      const targetDir = path.join(config.uploadDir, targetFolder);
      const newPath = path.join(targetDir, filename);

      // Đảm bảo thư mục đích tồn tại
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }

      // Di chuyển tệp
      fs.renameSync(oldPath, newPath);

      // Cập nhật thông tin hình ảnh
      image.path = newPath;
      image.folder = targetFolder;
      image.url = `/uploads/${targetFolder}/${filename}`;

      // Di chuyển các tệp liên quan nếu có
      if (image.optimized && image.optimizedPath) {
        const oldOptPath = image.optimizedPath;
        const optFilename = path.basename(oldOptPath);
        const newOptPath = path.join(targetDir, optFilename);
        fs.renameSync(oldOptPath, newOptPath);
        image.optimizedPath = newOptPath;
      }

      if (image.thumbnailPath) {
        const oldThumbPath = image.thumbnailPath;
        const thumbFilename = path.basename(oldThumbPath);
        const newThumbPath = path.join(targetDir, thumbFilename);
        fs.renameSync(oldThumbPath, newThumbPath);
        image.thumbnailPath = newThumbPath;
      }

      logInfo(`Đã di chuyển hình ảnh ${id} sang thư mục ${targetFolder}`);

      return image;
    } catch (error) {
      logError(`Lỗi khi di chuyển hình ảnh ${id}`, error);
      throw error;
    }
  }

  /**
   * Xóa hình ảnh
   * @param id ID hình ảnh
   */
  public async deleteImage(id: string): Promise<boolean> {
    try {
      const image = this.images.get(id);

      if (!image) {
        return false;
      }

      // Xóa tệp gốc
      try {
        await unlink(image.path);
      } catch (error) {
        logError(`Lỗi khi xóa tệp gốc: ${image.path}`, error);
      }

      // Xóa tệp đã tối ưu nếu có
      if (image.optimized && image.optimizedPath) {
        try {
          await unlink(image.optimizedPath);
        } catch (error) {
          logError(`Lỗi khi xóa tệp đã tối ưu: ${image.optimizedPath}`, error);
        }
      }

      // Xóa hình ảnh thu nhỏ nếu có
      if (image.thumbnailPath) {
        try {
          await unlink(image.thumbnailPath);
        } catch (error) {
          logError(
            `Lỗi khi xóa hình ảnh thu nhỏ: ${image.thumbnailPath}`,
            error
          );
        }
      }

      // Xóa khỏi bộ nhớ
      this.images.delete(id);

      logInfo(`Đã xóa hình ảnh: ${id}`);

      return true;
    } catch (error) {
      logError(`Lỗi khi xóa hình ảnh ${id}`, error);
      throw error;
    }
  }

  /**
   * Quét thư mục tải lên để đồng bộ hóa với dữ liệu trong bộ nhớ
   */
  public async scanUploadDirectory(): Promise<void> {
    try {
      // Đọc thư mục uploads
      const uploadDir = config.uploadDir;
      const items = await readdir(uploadDir);

      for (const item of items) {
        const itemPath = path.join(uploadDir, item);
        const itemStat = await stat(itemPath);

        if (itemStat.isDirectory()) {
          // Đây là một thư mục, quét các tệp trong đó
          const folderName = item;
          const files = await readdir(itemPath);

          for (const file of files) {
            const filePath = path.join(itemPath, file);
            const fileStat = await stat(filePath);

            if (fileStat.isFile()) {
              const extension = path.extname(file).toLowerCase();

              // Chỉ xử lý các tệp hình ảnh được hỗ trợ
              if (config.allowedExtensions.includes(extension)) {
                // Kiểm tra xem hình ảnh đã tồn tại trong bộ nhớ chưa
                const existingImage = Array.from(this.images.values()).find(
                  (img) => img.path === filePath
                );

                if (!existingImage) {
                  // Tạo hình ảnh mới
                  const image = new Image({
                    filename: file,
                    originalname: file,
                    mimetype:
                      extension === ".jpg" || extension === ".jpeg"
                        ? "image/jpeg"
                        : "image/png",
                    path: filePath,
                    size: fileStat.size,
                    extension: extension,
                    folder: folderName,
                    uploadDate: fileStat.birthtime,
                  });

                  // Kiểm tra xem có tồn tại tệp đã tối ưu không
                  const optimizedPath = path.join(
                    itemPath,
                    `optimized-${file}`
                  );
                  if (fs.existsSync(optimizedPath)) {
                    image.optimized = true;
                    image.optimizedPath = optimizedPath;
                  }

                  // Kiểm tra xem có tồn tại hình ảnh thu nhỏ không
                  const thumbnailPath = path.join(
                    itemPath,
                    `thumbnail-${file}`
                  );
                  if (fs.existsSync(thumbnailPath)) {
                    image.thumbnailPath = thumbnailPath;
                  }

                  // Tạo URL truy cập
                  image.url = `/uploads/${folderName}/${file}`;

                  // Lưu hình ảnh vào bộ nhớ
                  this.images.set(image.id, image);

                  logInfo(`Đã quét thấy hình ảnh mới: ${filePath}`);
                }
              }
            }
          }
        }
      }
    } catch (error) {
      logError("Lỗi khi quét thư mục uploads", error);
      throw error;
    }
  }
}

export default new ImageService();
