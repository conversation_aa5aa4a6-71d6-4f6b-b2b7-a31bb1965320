{"level":"info","message":"<PERSON><PERSON> tạo thư mục: C:\\Users\\<USER>\\Desktop\\server-upload\\src\\public\\uploads\\default","service":"image-server","timestamp":"2025-08-07 18:07:26"}
{"level":"info","message":"<PERSON><PERSON><PERSON> chủ đang chạy tại http://localhost:3000","service":"image-server","timestamp":"2025-08-07 18:07:26"}
{"level":"info","message":"<PERSON><PERSON> lên lịch sao lưu tự động với biểu thức: 0 0 * * *","service":"image-server","timestamp":"2025-08-07 18:07:26"}
{"level":"info","message":"Đã lên lịch sao lưu tự động với biểu thức: 0 0 * * *","service":"image-server","timestamp":"2025-08-07 18:07:26"}
{"ip":"::1","level":"info","message":"GET /","service":"image-server","timestamp":"2025-08-07 18:07:41"}
{"level":"error","message":"Lỗi ứng dụng:: C:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\index.ejs:1\n >> 1| <%- include('layouts/main', { title: 'Image Management Server - Trang chủ',\r\n    2| pageTitle: 'Hệ thống quản lý hình ảnh' }) %>\r\n    3| \r\n    4| <div class=\"prose dark:prose-invert max-w-none\">\r\n\nC:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\layouts\\main.ejs:147\n    145| \r\n    146|       <!-- Nội dung trang -->\r\n >> 147|       <%- body %>\r\n    148|     </main>\r\n    149| \r\n    150|     <!-- Footer -->\r\n\nbody is not defined","service":"image-server","stack":"ReferenceError: C:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\index.ejs:1\n >> 1| <%- include('layouts/main', { title: 'Image Management Server - Trang chủ',\r\n    2| pageTitle: 'Hệ thống quản lý hình ảnh' }) %>\r\n    3| \r\n    4| <div class=\"prose dark:prose-invert max-w-none\">\r\n\nC:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\layouts\\main.ejs:147\n    145| \r\n    146|       <!-- Nội dung trang -->\r\n >> 147|       <%- body %>\r\n    148|     </main>\r\n    149| \r\n    150|     <!-- Footer -->\r\n\nbody is not defined\n    at eval (\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\server-upload\\\\src\\\\views\\\\layouts\\\\main.ejs\":24:17)\n    at main (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at include (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:701:39)\n    at eval (\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\server-upload\\\\src\\\\views\\\\index.ejs\":10:17)\n    at index (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:274:36)\n    at View.exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\application.js:609:3)","timestamp":"2025-08-07 18:07:41"}
{"ip":"::1","level":"info","message":"GET /","service":"image-server","timestamp":"2025-08-07 18:10:12"}
{"level":"error","message":"Lỗi ứng dụng:: C:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\index.ejs:1\n >> 1| <%- include('layouts/main', { title: 'Image Management Server - Trang chủ',\r\n    2| pageTitle: 'Hệ thống quản lý hình ảnh' }) %>\r\n    3| \r\n    4| <div class=\"prose dark:prose-invert max-w-none\">\r\n\nC:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\layouts\\main.ejs:147\n    145| \r\n    146|       <!-- Nội dung trang -->\r\n >> 147|       <%- body %>\r\n    148|     </main>\r\n    149| \r\n    150|     <!-- Footer -->\r\n\nbody is not defined","service":"image-server","stack":"ReferenceError: C:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\index.ejs:1\n >> 1| <%- include('layouts/main', { title: 'Image Management Server - Trang chủ',\r\n    2| pageTitle: 'Hệ thống quản lý hình ảnh' }) %>\r\n    3| \r\n    4| <div class=\"prose dark:prose-invert max-w-none\">\r\n\nC:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\layouts\\main.ejs:147\n    145| \r\n    146|       <!-- Nội dung trang -->\r\n >> 147|       <%- body %>\r\n    148|     </main>\r\n    149| \r\n    150|     <!-- Footer -->\r\n\nbody is not defined\n    at eval (\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\server-upload\\\\src\\\\views\\\\layouts\\\\main.ejs\":24:17)\n    at main (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at include (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:701:39)\n    at eval (\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\server-upload\\\\src\\\\views\\\\index.ejs\":10:17)\n    at index (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:274:36)\n    at View.exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\application.js:609:3)","timestamp":"2025-08-07 18:10:12"}
{"ip":"::1","level":"info","message":"GET /favicon.ico","service":"image-server","timestamp":"2025-08-07 18:10:12"}
{"ip":"::1","level":"info","message":"GET /","service":"image-server","timestamp":"2025-08-07 18:10:13"}
{"level":"error","message":"Lỗi ứng dụng:: C:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\index.ejs:1\n >> 1| <%- include('layouts/main', { title: 'Image Management Server - Trang chủ',\r\n    2| pageTitle: 'Hệ thống quản lý hình ảnh' }) %>\r\n    3| \r\n    4| <div class=\"prose dark:prose-invert max-w-none\">\r\n\nC:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\layouts\\main.ejs:147\n    145| \r\n    146|       <!-- Nội dung trang -->\r\n >> 147|       <%- body %>\r\n    148|     </main>\r\n    149| \r\n    150|     <!-- Footer -->\r\n\nbody is not defined","service":"image-server","stack":"ReferenceError: C:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\index.ejs:1\n >> 1| <%- include('layouts/main', { title: 'Image Management Server - Trang chủ',\r\n    2| pageTitle: 'Hệ thống quản lý hình ảnh' }) %>\r\n    3| \r\n    4| <div class=\"prose dark:prose-invert max-w-none\">\r\n\nC:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\layouts\\main.ejs:147\n    145| \r\n    146|       <!-- Nội dung trang -->\r\n >> 147|       <%- body %>\r\n    148|     </main>\r\n    149| \r\n    150|     <!-- Footer -->\r\n\nbody is not defined\n    at eval (\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\server-upload\\\\src\\\\views\\\\layouts\\\\main.ejs\":24:17)\n    at main (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at include (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:701:39)\n    at eval (\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\server-upload\\\\src\\\\views\\\\index.ejs\":10:17)\n    at index (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:274:36)\n    at View.exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\application.js:609:3)","timestamp":"2025-08-07 18:10:13"}
{"ip":"::1","level":"info","message":"GET /","service":"image-server","timestamp":"2025-08-07 18:10:24"}
{"level":"error","message":"Lỗi ứng dụng:: C:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\index.ejs:1\n >> 1| <%- include('layouts/main', { title: 'Image Management Server - Trang chủ',\r\n    2| pageTitle: 'Hệ thống quản lý hình ảnh' }) %>\r\n    3| \r\n    4| <div class=\"prose dark:prose-invert max-w-none\">\r\n\nC:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\layouts\\main.ejs:147\n    145| \r\n    146|       <!-- Nội dung trang -->\r\n >> 147|       <%- body %>\r\n    148|     </main>\r\n    149| \r\n    150|     <!-- Footer -->\r\n\nbody is not defined","service":"image-server","stack":"ReferenceError: C:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\index.ejs:1\n >> 1| <%- include('layouts/main', { title: 'Image Management Server - Trang chủ',\r\n    2| pageTitle: 'Hệ thống quản lý hình ảnh' }) %>\r\n    3| \r\n    4| <div class=\"prose dark:prose-invert max-w-none\">\r\n\nC:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\layouts\\main.ejs:147\n    145| \r\n    146|       <!-- Nội dung trang -->\r\n >> 147|       <%- body %>\r\n    148|     </main>\r\n    149| \r\n    150|     <!-- Footer -->\r\n\nbody is not defined\n    at eval (\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\server-upload\\\\src\\\\views\\\\layouts\\\\main.ejs\":24:17)\n    at main (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at include (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:701:39)\n    at eval (\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\server-upload\\\\src\\\\views\\\\index.ejs\":10:17)\n    at index (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:703:17)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:274:36)\n    at View.exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\application.js:609:3)","timestamp":"2025-08-07 18:10:24"}
{"ip":"::1","level":"info","message":"GET /","service":"image-server","timestamp":"2025-08-07 18:12:07"}
{"level":"error","message":"Lỗi ứng dụng:: Invalid or unexpected token in C:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\index.ejs while compiling ejs\n\nIf the above error is not helpful, you may want to try EJS-Lint:\nhttps://github.com/RyanZim/EJS-Lint\nOr, if you meant to create an async function, pass `async: true` as an option.","service":"image-server","stack":"SyntaxError: Invalid or unexpected token in C:\\Users\\<USER>\\Desktop\\server-upload\\src\\views\\index.ejs while compiling ejs\n\nIf the above error is not helpful, you may want to try EJS-Lint:\nhttps://github.com/RyanZim/EJS-Lint\nOr, if you meant to create an async function, pass `async: true` as an option.\n    at new Function (<anonymous>)\n    at Template.compile (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:673:12)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:398:16)\n    at handleCache (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:235:18)\n    at tryHandleCache (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:274:16)\n    at View.exports.renderFile [as engine] (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\ejs\\lib\\ejs.js:491:10)\n    at View.render (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\view.js:135:8)\n    at tryRender (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\application.js:657:10)\n    at Function.render (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\application.js:609:3)\n    at ServerResponse.render (C:\\Users\\<USER>\\Desktop\\server-upload\\node_modules\\express\\lib\\response.js:1049:7)","timestamp":"2025-08-07 18:12:07"}
