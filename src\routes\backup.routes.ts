import { Router } from "express";
import backupController from "../controllers/backup.controller";

const router = Router();

// Thực hiện sao lưu thủ công
router.post("/manual", backupController.manualBackup);

// <PERSON><PERSON><PERSON> lịch sao lưu tự động
router.post("/schedule", backupController.scheduleBackup);

// Dừng lịch trình sao lưu tự động
router.post("/stop-schedule", backupController.stopScheduledBackup);

// L<PERSON><PERSON> danh sách các bản sao lưu
router.get("/", backupController.getBackupList);

// Xóa một bản sao lưu
router.delete("/", backupController.deleteBackup);

export default router;
