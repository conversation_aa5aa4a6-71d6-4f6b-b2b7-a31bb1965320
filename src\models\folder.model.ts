import { z } from "zod";

// Schema xác thực cho dữ liệu thư mục
export const folderSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "<PERSON>ê<PERSON> thư mục không được để trống"),
  path: z.string(),
  parentId: z.string().nullable().default(null),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
  imageCount: z.number().default(0),
});

// Lấy kiểu TypeScript từ schema Zod
export type FolderModel = z.infer<typeof folderSchema>;

/**
 * Lớp đại diện cho một thư mục
 */
class Folder implements FolderModel {
  id: string;
  name: string;
  path: string;
  parentId: string | null;
  createdAt: Date;
  updatedAt: Date;
  imageCount: number;

  /**
   * Tạo đối tượng thư mục mới
   * @param data Dữ liệu thư mục
   */
  constructor(data: Partial<FolderModel>) {
    // Thiết lập ID mặc định nếu không được cung cấp
    this.id = data.id || this.generateId();
    this.name = data.name || "";
    this.path = data.path || "";
    this.parentId = data.parentId !== undefined ? data.parentId : null;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();
    this.imageCount = data.imageCount || 0;

    // Xác thực dữ liệu với schema
    this.validate();
  }

  /**
   * Xác thực dữ liệu đối tượng thư mục
   * @throws {Error} Ném lỗi nếu dữ liệu không hợp lệ
   */
  validate(): void {
    try {
      folderSchema.parse(this);
    } catch (error) {
      throw new Error(
        `Dữ liệu thư mục không hợp lệ: ${
          error instanceof Error ? error.message : "Lỗi không xác định"
        }`
      );
    }
  }

  /**
   * Tạo ID duy nhất cho thư mục
   * @returns ID duy nhất
   */
  private generateId(): string {
    return `folder_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Chuyển đối tượng thành JSON
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      name: this.name,
      path: this.path,
      parentId: this.parentId,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      imageCount: this.imageCount,
    };
  }
}

export default Folder;
