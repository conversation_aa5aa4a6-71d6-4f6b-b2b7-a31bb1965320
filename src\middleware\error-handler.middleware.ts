import { Request, Response, NextFunction } from "express";
import { logError } from "../utils/logger";
import multer from "multer";
import { ZodError } from "zod";

// Interface để khai báo loại lỗi tùy chỉnh
interface CustomError extends Error {
  statusCode?: number;
  code?: string;
}

// Middleware xử lý lỗi tổng quát
export const errorHandler = (
  err: CustomError,
  _req: Request,
  res: Response,
  _next: NextFunction
): void => {
  // Ghi log lỗi
  logError("Lỗi ứng dụng:", err);

  // Thiết lập mã trạng thái mặc định
  let statusCode = err.statusCode || 500;
  let errorMessage = err.message || "Đã xảy ra lỗi server";

  // Xử lý các loại lỗi cụ thể

  // Lỗi Multer (tải lên tệp)
  if (err instanceof multer.MulterError) {
    statusCode = 400;
    switch (err.code) {
      case "LIMIT_FILE_SIZE":
        errorMessage = "Tệp quá lớn, vượt quá giới hạn cho phép (5MB)";
        break;
      case "LIMIT_UNEXPECTED_FILE":
        errorMessage = "Tệp không được mong đợi";
        break;
      default:
        errorMessage = `Lỗi tải lên tệp: ${err.message}`;
    }
  }

  // Lỗi xác thực Zod
  if (err instanceof ZodError) {
    statusCode = 400;
    errorMessage = `Lỗi xác thực dữ liệu: ${
      err.errors[0]?.message || "Dữ liệu không hợp lệ"
    }`;
  }

  // Trả về phản hồi lỗi
  res.status(statusCode).json({
    success: false,
    error: errorMessage,
    stack: process.env.NODE_ENV === "development" ? err.stack : undefined,
  });
};

// Middleware xử lý đường dẫn không tồn tại
export const notFoundHandler = (req: Request, res: Response): void => {
  res.status(404).json({
    success: false,
    error: `Không tìm thấy đường dẫn: ${req.originalUrl}`,
  });
};

// Middleware bắt lỗi bất đồng bộ (để tránh try-catch lặp đi lặp lại)
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
